#!/usr/bin/env node

/**
 * IPC 命令全局替换脚本
 * 将硬编码的字符串替换为常量引用
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// 定义替换映射
const IPC_REPLACEMENTS = {
  // 版本管理
  "'version:get-current'": 'IPC_COMMANDS.VERSION.GET_CURRENT',
  '"version:get-current"': 'IPC_COMMANDS.VERSION.GET_CURRENT',
  "'version:bump'": 'IPC_COMMANDS.VERSION.BUMP',
  '"version:bump"': 'IPC_COMMANDS.VERSION.BUMP',
  "'version:get-history'": 'IPC_COMMANDS.VERSION.GET_HISTORY',
  '"version:get-history"': 'IPC_COMMANDS.VERSION.GET_HISTORY',
  "'version:create-tag'": 'IPC_COMMANDS.VERSION.CREATE_TAG',
  '"version:create-tag"': 'IPC_COMMANDS.VERSION.CREATE_TAG',
  "'version:generate-changelog'": 'IPC_COMMANDS.VERSION.GENERATE_CHANGELOG',
  '"version:generate-changelog"': 'IPC_COMMANDS.VERSION.GENERATE_CHANGELOG',
  "'version:release'": 'IPC_COMMANDS.VERSION.RELEASE',
  '"version:release"': 'IPC_COMMANDS.VERSION.RELEASE',
  "'version:get-formatted-list'": 'IPC_COMMANDS.VERSION.GET_FORMATTED_LIST',
  '"version:get-formatted-list"': 'IPC_COMMANDS.VERSION.GET_FORMATTED_LIST',

  // 构建管理
  "'build:start'": 'IPC_COMMANDS.BUILD.START',
  '"build:start"': 'IPC_COMMANDS.BUILD.START',
  "'build:cancel'": 'IPC_COMMANDS.BUILD.CANCEL',
  '"build:cancel"': 'IPC_COMMANDS.BUILD.CANCEL',
  "'build:get-stats'": 'IPC_COMMANDS.BUILD.GET_STATS',
  '"build:get-stats"': 'IPC_COMMANDS.BUILD.GET_STATS',
  "'build:clean'": 'IPC_COMMANDS.BUILD.CLEAN',
  '"build:clean"': 'IPC_COMMANDS.BUILD.CLEAN',
  "'build:get-history'": 'IPC_COMMANDS.BUILD.GET_HISTORY',
  '"build:get-history"': 'IPC_COMMANDS.BUILD.GET_HISTORY',
  "'build:build-all'": 'IPC_COMMANDS.BUILD.BUILD_ALL',
  '"build:build-all"': 'IPC_COMMANDS.BUILD.BUILD_ALL',

  // 回滚管理
  "'rollback:list'": 'IPC_COMMANDS.ROLLBACK.LIST',
  '"rollback:list"': 'IPC_COMMANDS.ROLLBACK.LIST',
  "'rollback:rollback-to'": 'IPC_COMMANDS.ROLLBACK.ROLLBACK_TO',
  '"rollback:rollback-to"': 'IPC_COMMANDS.ROLLBACK.ROLLBACK_TO',
  "'rollback:rollback-last'": 'IPC_COMMANDS.ROLLBACK.ROLLBACK_LAST',
  '"rollback:rollback-last"': 'IPC_COMMANDS.ROLLBACK.ROLLBACK_LAST',
  "'rollback:get-status'": 'IPC_COMMANDS.ROLLBACK.GET_STATUS',
  '"rollback:get-status"': 'IPC_COMMANDS.ROLLBACK.GET_STATUS',
  "'rollback:validate'": 'IPC_COMMANDS.ROLLBACK.VALIDATE',
  '"rollback:validate"': 'IPC_COMMANDS.ROLLBACK.VALIDATE',
  "'rollback:create-checkpoint'": 'IPC_COMMANDS.ROLLBACK.CREATE_CHECKPOINT',
  '"rollback:create-checkpoint"': 'IPC_COMMANDS.ROLLBACK.CREATE_CHECKPOINT',

  // 热更新管理
  "'hotupdate:generate-manifest'": 'IPC_COMMANDS.HOTUPDATE.GENERATE_MANIFEST',
  '"hotupdate:generate-manifest"': 'IPC_COMMANDS.HOTUPDATE.GENERATE_MANIFEST',
  "'hotupdate:create-patch'": 'IPC_COMMANDS.HOTUPDATE.CREATE_PATCH',
  '"hotupdate:create-patch"': 'IPC_COMMANDS.HOTUPDATE.CREATE_PATCH',
  "'hotupdate:verify'": 'IPC_COMMANDS.HOTUPDATE.VERIFY',
  '"hotupdate:verify"': 'IPC_COMMANDS.HOTUPDATE.VERIFY',
  "'hotupdate:clean'": 'IPC_COMMANDS.HOTUPDATE.CLEAN',
  '"hotupdate:clean"': 'IPC_COMMANDS.HOTUPDATE.CLEAN',
  "'hotupdate:compare'": 'IPC_COMMANDS.HOTUPDATE.COMPARE',
  '"hotupdate:compare"': 'IPC_COMMANDS.HOTUPDATE.COMPARE',
  "'hotupdate:get-config'": 'IPC_COMMANDS.HOTUPDATE.GET_CONFIG',
  '"hotupdate:get-config"': 'IPC_COMMANDS.HOTUPDATE.GET_CONFIG',
  "'hotupdate:check-changes'": 'IPC_COMMANDS.HOTUPDATE.CHECK_CHANGES',
  '"hotupdate:check-changes"': 'IPC_COMMANDS.HOTUPDATE.CHECK_CHANGES',

  // 部署管理
  "'deploy:start'": 'IPC_COMMANDS.DEPLOY.START',
  '"deploy:start"': 'IPC_COMMANDS.DEPLOY.START',
  "'deploy:cancel'": 'IPC_COMMANDS.DEPLOY.CANCEL',
  '"deploy:cancel"': 'IPC_COMMANDS.DEPLOY.CANCEL',
  "'deploy:get-status'": 'IPC_COMMANDS.DEPLOY.GET_STATUS',
  '"deploy:get-status"': 'IPC_COMMANDS.DEPLOY.GET_STATUS',
  "'deploy:get-history'": 'IPC_COMMANDS.DEPLOY.GET_HISTORY',
  '"deploy:get-history"': 'IPC_COMMANDS.DEPLOY.GET_HISTORY',
  "'deploy:get-environments'": 'IPC_COMMANDS.DEPLOY.GET_ENVIRONMENTS',
  '"deploy:get-environments"': 'IPC_COMMANDS.DEPLOY.GET_ENVIRONMENTS',
  "'deploy:validate-config'": 'IPC_COMMANDS.DEPLOY.VALIDATE_CONFIG',
  '"deploy:validate-config"': 'IPC_COMMANDS.DEPLOY.VALIDATE_CONFIG',

  // 配置管理
  "'config:get'": 'IPC_COMMANDS.CONFIG.GET',
  '"config:get"': 'IPC_COMMANDS.CONFIG.GET',
  "'config:set'": 'IPC_COMMANDS.CONFIG.SET',
  '"config:set"': 'IPC_COMMANDS.CONFIG.SET',
  "'config:validate'": 'IPC_COMMANDS.CONFIG.VALIDATE',
  '"config:validate"': 'IPC_COMMANDS.CONFIG.VALIDATE',
  "'config:export'": 'IPC_COMMANDS.CONFIG.EXPORT',
  '"config:export"': 'IPC_COMMANDS.CONFIG.EXPORT',
  "'config:import'": 'IPC_COMMANDS.CONFIG.IMPORT',
  '"config:import"': 'IPC_COMMANDS.CONFIG.IMPORT',
  "'config:reset'": 'IPC_COMMANDS.CONFIG.RESET',
  '"config:reset"': 'IPC_COMMANDS.CONFIG.RESET',

  // 系统操作
  "'select-project-directory'": 'IPC_COMMANDS.PROJECT.SELECT_DIRECTORY',
  '"select-project-directory"': 'IPC_COMMANDS.PROJECT.SELECT_DIRECTORY',
  "'get-current-project'": 'IPC_COMMANDS.PROJECT.GET_CURRENT',
  '"get-current-project"': 'IPC_COMMANDS.PROJECT.GET_CURRENT',
  "'get-project-info'": 'IPC_COMMANDS.PROJECT.GET_INFO',
  '"get-project-info"': 'IPC_COMMANDS.PROJECT.GET_INFO',
  "'open-external'": 'IPC_COMMANDS.SYSTEM.OPEN_EXTERNAL',
  '"open-external"': 'IPC_COMMANDS.SYSTEM.OPEN_EXTERNAL',
  "'show-item-in-folder'": 'IPC_COMMANDS.SYSTEM.SHOW_ITEM_IN_FOLDER',
  '"show-item-in-folder"': 'IPC_COMMANDS.SYSTEM.SHOW_ITEM_IN_FOLDER',
  "'quit-app'": 'IPC_COMMANDS.SYSTEM.QUIT_APP',
  '"quit-app"': 'IPC_COMMANDS.SYSTEM.QUIT_APP',

  // 事件名称
  "'build-progress'": 'IPC_COMMANDS.EVENTS.BUILD_PROGRESS',
  '"build-progress"': 'IPC_COMMANDS.EVENTS.BUILD_PROGRESS',
  "'build-complete'": 'IPC_COMMANDS.EVENTS.BUILD_COMPLETE',
  '"build-complete"': 'IPC_COMMANDS.EVENTS.BUILD_COMPLETE',
  "'version-changed'": 'IPC_COMMANDS.EVENTS.VERSION_CHANGED',
  '"version-changed"': 'IPC_COMMANDS.EVENTS.VERSION_CHANGED',
  "'deploy-progress'": 'IPC_COMMANDS.EVENTS.DEPLOY_PROGRESS',
  '"deploy-progress"': 'IPC_COMMANDS.EVENTS.DEPLOY_PROGRESS',
  "'deploy-complete'": 'IPC_COMMANDS.EVENTS.DEPLOY_COMPLETE',
  '"deploy-complete"': 'IPC_COMMANDS.EVENTS.DEPLOY_COMPLETE'
};

// 需要添加 import 的文件
const IMPORT_STATEMENT = "import { IPC_COMMANDS } from '../constants/ipc-commands';";
const IMPORT_STATEMENT_MAIN = "import { IPC_COMMANDS } from '../../shared/constants/ipc-commands';";

/**
 * 检查文件是否需要添加 import
 */
function needsImport(content) {
  return content.includes('IPC_COMMANDS.') && !content.includes("from '../constants/ipc-commands'") && !content.includes("from '../../shared/constants/ipc-commands'");
}

/**
 * 添加 import 语句
 */
function addImport(content, filePath) {
  const lines = content.split('\n');
  let importAdded = false;
  let result = [];
  
  // 确定使用哪个 import 路径
  const isMainProcess = filePath.includes('/main/');
  const importToAdd = isMainProcess ? IMPORT_STATEMENT_MAIN : IMPORT_STATEMENT;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // 在最后一个 import 语句后添加我们的 import
    if (!importAdded && line.startsWith('import ') && 
        (i === lines.length - 1 || !lines[i + 1].startsWith('import '))) {
      result.push(line);
      result.push(importToAdd);
      importAdded = true;
    } else {
      result.push(line);
    }
  }
  
  // 如果没有找到 import 语句，在文件开头添加
  if (!importAdded) {
    result.unshift(importToAdd);
    result.unshift('');
  }
  
  return result.join('\n');
}

/**
 * 处理单个文件
 */
function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 执行替换
    for (const [oldStr, newStr] of Object.entries(IPC_REPLACEMENTS)) {
      if (content.includes(oldStr)) {
        content = content.replace(new RegExp(oldStr.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), newStr);
        modified = true;
      }
    }
    
    // 如果有修改且需要添加 import
    if (modified && needsImport(content)) {
      content = addImport(content, filePath);
    }
    
    // 写回文件
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已更新: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
    return false;
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🚀 开始 IPC 命令全局替换...\n');
  
  // 定义要处理的文件模式
  const patterns = [
    'src/**/*.ts',
    'src/**/*.vue',
    '!src/shared/constants/ipc-commands.ts', // 排除常量定义文件
    '!node_modules/**',
    '!dist/**'
  ];
  
  let totalFiles = 0;
  let modifiedFiles = 0;
  
  // 处理每个模式
  patterns.forEach(pattern => {
    if (pattern.startsWith('!')) return; // 跳过排除模式
    
    const files = glob.sync(pattern, { cwd: process.cwd() });
    
    files.forEach(file => {
      // 检查是否被排除
      const isExcluded = patterns.some(p => 
        p.startsWith('!') && glob.minimatch(file, p.substring(1))
      );
      
      if (!isExcluded) {
        totalFiles++;
        if (processFile(file)) {
          modifiedFiles++;
        }
      }
    });
  });
  
  console.log(`\n📊 替换完成:`);
  console.log(`   总文件数: ${totalFiles}`);
  console.log(`   修改文件数: ${modifiedFiles}`);
  console.log(`   未修改文件数: ${totalFiles - modifiedFiles}`);
  
  if (modifiedFiles > 0) {
    console.log('\n🎉 IPC 命令替换成功！');
    console.log('💡 请检查修改的文件，确保 import 路径正确。');
  } else {
    console.log('\n✨ 没有找到需要替换的内容。');
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { processFile, IPC_REPLACEMENTS };
