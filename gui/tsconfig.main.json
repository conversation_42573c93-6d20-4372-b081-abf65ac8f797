{
  "compilerOptions": {
    "target": "ES2020",
    "module": "CommonJS",
    "lib": ["ES2020"],
    "outDir": "./dist",
    "rootDir": "./src",
    "noEmit": false,
    "strict": true,
    "skipLibCheck": true,
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,

    /* Node.js specific */
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,

    /* Type definitions */
    "types": ["node", "electron"],

    /* Path mapping */
    "baseUrl": "./src",
    "paths": {
      "@shared/*": ["shared/*"],
      "@main/*": ["main/*"]
    }
  },
  "include": [
    "src/main/**/*.ts",
    "src/shared/**/*.ts"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "release",
    "src/renderer"
  ]
}
