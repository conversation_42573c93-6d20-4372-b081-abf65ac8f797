/**
 * 环境检测工具
 * 用于检测当前运行环境（主进程/渲染进程）
 */

// 检测是否在浏览器环境中
export const isBrowser = typeof window !== 'undefined';

// 检测是否在 Node.js 环境中
export const isNode = typeof process !== 'undefined' && process.versions && process.versions.node;

// 检测是否在 Electron 主进程中
export const isMainProcess = isNode && process.type === 'browser';

// 检测是否在 Electron 渲染进程中
export const isRendererProcess = isBrowser && typeof window.electronAPI !== 'undefined';

// 检测是否在 Electron 环境中
export const isElectron = isMainProcess || isRendererProcess;

// 安全的浏览器 API 访问
export const safeWindow = isBrowser ? window : undefined;
export const safeNavigator = isBrowser ? navigator : undefined;
export const safeDocument = isBrowser ? document : undefined;

// 安全的 Node.js API 访问
export const safeProcess = isNode ? process : undefined;

/**
 * 安全地获取用户代理字符串
 */
export function getUserAgent(): string {
  if (safeNavigator) {
    return safeNavigator.userAgent;
  }
  if (safeProcess) {
    return `Node.js/${safeProcess.version} (${safeProcess.platform}; ${safeProcess.arch})`;
  }
  return 'Unknown';
}

/**
 * 安全地获取当前 URL
 */
export function getCurrentURL(): string {
  if (isBrowser && safeWindow) {
    return safeWindow.location.href;
  }
  return 'file://unknown';
}

/**
 * 安全地访问剪贴板
 */
export async function writeToClipboard(text: string): Promise<boolean> {
  if (safeNavigator && safeNavigator.clipboard) {
    try {
      await safeNavigator.clipboard.writeText(text);
      return true;
    } catch (error) {
      console.warn('Failed to write to clipboard:', error);
      return false;
    }
  }
  return false;
}

/**
 * 安全地聚焦窗口
 */
export function focusWindow(): void {
  if (safeWindow && safeWindow.focus) {
    safeWindow.focus();
  }
}

/**
 * 安全地添加事件监听器
 */
export function addEventListener(
  event: string, 
  handler: EventListener, 
  options?: boolean | AddEventListenerOptions
): void {
  if (safeWindow && safeWindow.addEventListener) {
    safeWindow.addEventListener(event, handler, options);
  }
}

/**
 * 安全地移除事件监听器
 */
export function removeEventListener(
  event: string, 
  handler: EventListener, 
  options?: boolean | EventListenerOptions
): void {
  if (safeWindow && safeWindow.removeEventListener) {
    safeWindow.removeEventListener(event, handler, options);
  }
}
