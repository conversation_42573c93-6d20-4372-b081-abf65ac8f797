/**
 * 统一日志工具
 * 提供分级日志记录和输出功能
 */

import { isRendererProcess } from './environment';

// ==================== 日志级别定义 ====================

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  FATAL = 4
}

export interface LogEntry {
  level: LogLevel;
  message: string;
  data?: any;
  timestamp: string;
  context?: string;
  stack?: string;
}

export interface LoggerConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableFile: boolean;
  maxEntries: number;
  dateFormat: string;
}

// ==================== 日志工具类 ====================

export class Logger {
  private static config: LoggerConfig = {
    level: LogLevel.INFO,
    enableConsole: true,
    enableFile: false,
    maxEntries: 1000,
    dateFormat: 'YYYY-MM-DD HH:mm:ss'
  };

  private static entries: LogEntry[] = [];
  private static listeners: Array<(entry: LogEntry) => void> = [];

  // ==================== 配置方法 ====================

  /**
   * 设置日志配置
   */
  static configure(config: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 设置日志级别
   */
  static setLevel(level: LogLevel): void {
    this.config.level = level;
  }

  /**
   * 启用/禁用控制台输出
   */
  static setConsoleEnabled(enabled: boolean): void {
    this.config.enableConsole = enabled;
  }

  // ==================== 日志记录方法 ====================

  /**
   * 调试日志
   */
  static debug(message: string, data?: any, context?: string): void {
    this.log(LogLevel.DEBUG, message, data, context);
  }

  /**
   * 信息日志
   */
  static info(message: string, data?: any, context?: string): void {
    this.log(LogLevel.INFO, message, data, context);
  }

  /**
   * 警告日志
   */
  static warn(message: string, data?: any, context?: string): void {
    this.log(LogLevel.WARN, message, data, context);
  }

  /**
   * 错误日志
   */
  static error(message: string, data?: any, context?: string): void {
    this.log(LogLevel.ERROR, message, data, context);
  }

  /**
   * 致命错误日志
   */
  static fatal(message: string, data?: any, context?: string): void {
    this.log(LogLevel.FATAL, message, data, context);
  }

  /**
   * 通用日志记录方法
   */
  static log(level: LogLevel, message: string, data?: any, context?: string): void {
    // 检查日志级别
    if (level < this.config.level) {
      return;
    }

    // 创建日志条目
    const entry: LogEntry = {
      level,
      message,
      data,
      timestamp: this.formatTimestamp(new Date()),
      context,
      stack: level >= LogLevel.ERROR ? this.getStackTrace() : undefined
    };

    // 添加到日志列表
    this.addEntry(entry);

    // 控制台输出
    if (this.config.enableConsole) {
      this.outputToConsole(entry);
    }

    // 文件输出（如果启用）
    if (this.config.enableFile) {
      this.outputToFile(entry);
    }

    // 通知监听器
    this.notifyListeners(entry);
  }

  // ==================== 日志查询方法 ====================

  /**
   * 获取所有日志条目
   */
  static getEntries(): LogEntry[] {
    return [...this.entries];
  }

  /**
   * 根据级别获取日志条目
   */
  static getEntriesByLevel(level: LogLevel): LogEntry[] {
    return this.entries.filter(entry => entry.level === level);
  }

  /**
   * 根据上下文获取日志条目
   */
  static getEntriesByContext(context: string): LogEntry[] {
    return this.entries.filter(entry => entry.context === context);
  }

  /**
   * 获取最近的日志条目
   */
  static getRecentEntries(count: number = 50): LogEntry[] {
    return this.entries.slice(-count);
  }

  /**
   * 搜索日志条目
   */
  static searchEntries(query: string): LogEntry[] {
    const lowerQuery = query.toLowerCase();
    return this.entries.filter(entry => 
      entry.message.toLowerCase().includes(lowerQuery) ||
      entry.context?.toLowerCase().includes(lowerQuery) ||
      (typeof entry.data === 'string' && entry.data.toLowerCase().includes(lowerQuery))
    );
  }

  /**
   * 清除日志条目
   */
  static clearEntries(): void {
    this.entries.splice(0);
  }

  // ==================== 监听器方法 ====================

  /**
   * 添加日志监听器
   */
  static addListener(listener: (entry: LogEntry) => void): void {
    this.listeners.push(listener);
  }

  /**
   * 移除日志监听器
   */
  static removeListener(listener: (entry: LogEntry) => void): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  // ==================== 导出方法 ====================

  /**
   * 导出日志为 JSON
   */
  static exportAsJSON(): string {
    return JSON.stringify(this.entries, null, 2);
  }

  /**
   * 导出日志为文本
   */
  static exportAsText(): string {
    return this.entries.map(entry => this.formatEntryAsText(entry)).join('\n');
  }

  /**
   * 导出日志为 CSV
   */
  static exportAsCSV(): string {
    const headers = ['Timestamp', 'Level', 'Context', 'Message', 'Data'];
    const rows = this.entries.map(entry => [
      entry.timestamp,
      LogLevel[entry.level],
      entry.context || '',
      entry.message,
      entry.data ? JSON.stringify(entry.data) : ''
    ]);

    return [headers, ...rows].map(row => 
      row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(',')
    ).join('\n');
  }

  // ==================== 私有方法 ====================

  /**
   * 添加日志条目
   */
  private static addEntry(entry: LogEntry): void {
    this.entries.push(entry);

    // 限制日志条目数量
    if (this.entries.length > this.config.maxEntries) {
      this.entries.shift();
    }
  }

  /**
   * 输出到控制台
   */
  private static outputToConsole(entry: LogEntry): void {
    const prefix = `[${entry.timestamp}] ${LogLevel[entry.level]}`;
    const context = entry.context ? ` [${entry.context}]` : '';
    const message = `${prefix}${context}: ${entry.message}`;

    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(message, entry.data);
        break;
      case LogLevel.INFO:
        console.info(message, entry.data);
        break;
      case LogLevel.WARN:
        console.warn(message, entry.data);
        break;
      case LogLevel.ERROR:
      case LogLevel.FATAL:
        console.error(message, entry.data);
        if (entry.stack) {
          console.error('Stack trace:', entry.stack);
        }
        break;
    }
  }

  /**
   * 输出到文件（占位符实现）
   */
  private static outputToFile(entry: LogEntry): void {
    // 在 Electron 环境中，可以通过 IPC 发送到主进程进行文件写入
    // 这里只是占位符实现
    if (window.electronAPI && window.electronAPI.invoke) {
      window.electronAPI.invoke('logger:write-to-file', entry).catch(console.error);
    }
  }

  /**
   * 通知监听器
   */
  private static notifyListeners(entry: LogEntry): void {
    this.listeners.forEach(listener => {
      try {
        listener(entry);
      } catch (error) {
        console.error('Error in log listener:', error);
      }
    });
  }

  /**
   * 格式化时间戳
   */
  private static formatTimestamp(date: Date): string {
    // 简单的时间格式化，可以根据需要使用更复杂的库
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    const milliseconds = String(date.getMilliseconds()).padStart(3, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
  }

  /**
   * 获取堆栈跟踪
   */
  private static getStackTrace(): string {
    try {
      throw new Error();
    } catch (error) {
      const stack = (error as Error).stack;
      if (stack) {
        // 移除前几行（Logger 内部调用）
        const lines = stack.split('\n');
        return lines.slice(3).join('\n');
      }
      return '';
    }
  }

  /**
   * 格式化日志条目为文本
   */
  private static formatEntryAsText(entry: LogEntry): string {
    const level = LogLevel[entry.level].padEnd(5);
    const context = entry.context ? ` [${entry.context}]` : '';
    let text = `${entry.timestamp} ${level}${context}: ${entry.message}`;

    if (entry.data) {
      text += `\n  Data: ${JSON.stringify(entry.data, null, 2)}`;
    }

    if (entry.stack) {
      text += `\n  Stack: ${entry.stack}`;
    }

    return text;
  }
}

// ==================== 便捷导出 ====================

export const log = Logger.log.bind(Logger);
export const debug = Logger.debug.bind(Logger);
export const info = Logger.info.bind(Logger);
export const warn = Logger.warn.bind(Logger);
export const error = Logger.error.bind(Logger);
export const fatal = Logger.fatal.bind(Logger);
