/**
 * IPC 命令常量定义
 * 集中管理所有前后端通信的命令名称，避免硬编码字符串
 */

// ==================== 项目管理命令 ====================
export const PROJECT_COMMANDS = {
  SELECT_DIRECTORY: 'select-project-directory',
  GET_CURRENT: 'get-current-project',
  GET_INFO: 'get-project-info'
} as const;

// ==================== 版本管理命令 ====================
export const VERSION_COMMANDS = {
  GET_CURRENT: 'version:get-current',
  BUMP: 'version:bump',
  GET_HISTORY: 'version:get-history',
  CREATE_TAG: 'version:create-tag',
  GENERATE_CHANGELOG: 'version:generate-changelog',
  RELEASE: 'version:release',
  GET_FORMATTED_LIST: 'version:get-formatted-list'
} as const;

// ==================== 构建管理命令 ====================
export const BUILD_COMMANDS = {
  START: 'build:start',
  CANCEL: 'build:cancel',
  GET_STATS: 'build:get-stats',
  CLEAN: 'build:clean',
  GET_HISTORY: 'build:get-history',
  BUILD_ALL: 'build:build-all'
} as const;

// ==================== 回滚管理命令 ====================
export const ROLLBACK_COMMANDS = {
  LIST: 'rollback:list',
  ROLLBACK_TO: 'rollback:rollback-to',
  ROLLBACK_LAST: 'rollback:rollback-last',
  GET_STATUS: 'rollback:get-status',
  VALIDATE: 'rollback:validate',
  CREATE_CHECKPOINT: 'rollback:create-checkpoint'
} as const;

// ==================== 热更新管理命令 ====================
export const HOTUPDATE_COMMANDS = {
  GENERATE_MANIFEST: 'hotupdate:generate-manifest',
  CREATE_PATCH: 'hotupdate:create-patch',
  VERIFY: 'hotupdate:verify',
  CLEAN: 'hotupdate:clean',
  COMPARE: 'hotupdate:compare',
  GET_CONFIG: 'hotupdate:get-config',
  CHECK_CHANGES: 'hotupdate:check-changes'
} as const;

// ==================== 部署管理命令 ====================
export const DEPLOY_COMMANDS = {
  START: 'deploy:start',
  CANCEL: 'deploy:cancel',
  GET_STATUS: 'deploy:get-status',
  GET_HISTORY: 'deploy:get-history',
  GET_ENVIRONMENTS: 'deploy:get-environments',
  VALIDATE_CONFIG: 'deploy:validate-config'
} as const;

// ==================== 配置管理命令 ====================
export const CONFIG_COMMANDS = {
  GET: 'config:get',
  SET: 'config:set',
  VALIDATE: 'config:validate',
  EXPORT: 'config:export',
  IMPORT: 'config:import',
  RESET: 'config:reset'
} as const;

// ==================== 系统操作命令 ====================
export const SYSTEM_COMMANDS = {
  OPEN_EXTERNAL: 'open-external',
  SHOW_ITEM_IN_FOLDER: 'show-item-in-folder',
  QUIT_APP: 'quit-app'
} as const;

// ==================== 事件名称 ====================
export const EVENT_NAMES = {
  BUILD_PROGRESS: 'build-progress',
  BUILD_COMPLETE: 'build-complete',
  VERSION_CHANGED: 'version-changed',
  DEPLOY_PROGRESS: 'deploy-progress',
  DEPLOY_COMPLETE: 'deploy-complete'
} as const;

// ==================== 统一导出 ====================
export const IPC_COMMANDS = {
  PROJECT: PROJECT_COMMANDS,
  VERSION: VERSION_COMMANDS,
  BUILD: BUILD_COMMANDS,
  ROLLBACK: ROLLBACK_COMMANDS,
  HOTUPDATE: HOTUPDATE_COMMANDS,
  DEPLOY: DEPLOY_COMMANDS,
  CONFIG: CONFIG_COMMANDS,
  SYSTEM: SYSTEM_COMMANDS,
  EVENTS: EVENT_NAMES
} as const;

// ==================== 类型定义 ====================
export type ProjectCommand = typeof PROJECT_COMMANDS[keyof typeof PROJECT_COMMANDS];
export type VersionCommand = typeof VERSION_COMMANDS[keyof typeof VERSION_COMMANDS];
export type BuildCommand = typeof BUILD_COMMANDS[keyof typeof BUILD_COMMANDS];
export type RollbackCommand = typeof ROLLBACK_COMMANDS[keyof typeof ROLLBACK_COMMANDS];
export type HotUpdateCommand = typeof HOTUPDATE_COMMANDS[keyof typeof HOTUPDATE_COMMANDS];
export type DeployCommand = typeof DEPLOY_COMMANDS[keyof typeof DEPLOY_COMMANDS];
export type ConfigCommand = typeof CONFIG_COMMANDS[keyof typeof CONFIG_COMMANDS];
export type SystemCommand = typeof SYSTEM_COMMANDS[keyof typeof SYSTEM_COMMANDS];
export type EventName = typeof EVENT_NAMES[keyof typeof EVENT_NAMES];

export type IPCCommand = 
  | ProjectCommand 
  | VersionCommand 
  | BuildCommand 
  | RollbackCommand 
  | HotUpdateCommand 
  | DeployCommand 
  | ConfigCommand 
  | SystemCommand;

// ==================== 辅助函数 ====================
/**
 * 验证 IPC 命令是否有效
 */
export function isValidIPCCommand(command: string): command is IPCCommand {
  const allCommands = [
    ...Object.values(PROJECT_COMMANDS),
    ...Object.values(VERSION_COMMANDS),
    ...Object.values(BUILD_COMMANDS),
    ...Object.values(ROLLBACK_COMMANDS),
    ...Object.values(HOTUPDATE_COMMANDS),
    ...Object.values(DEPLOY_COMMANDS),
    ...Object.values(CONFIG_COMMANDS),
    ...Object.values(SYSTEM_COMMANDS)
  ];
  
  return allCommands.includes(command as any);
}

/**
 * 获取命令的命名空间
 */
export function getCommandNamespace(command: IPCCommand): string {
  const parts = command.split(':');
  return parts.length > 1 ? parts[0] : 'system';
}

/**
 * 获取命令的操作名称
 */
export function getCommandAction(command: IPCCommand): string {
  const parts = command.split(':');
  return parts.length > 1 ? parts[1] : parts[0];
}
