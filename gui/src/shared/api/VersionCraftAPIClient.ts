/**
 * Version-Craft 统一 API 客户端
 * 提供类型安全的 IPC 通信接口
 */

import { isRendererProcess, safeWindow } from '../utils/environment';
import {
  APIResponse,
  APIError,
  BumpOptions,
  TagOptions,
  ChangelogOptions,
  ReleaseOptions,
  VersionInfo,
  VersionHistoryItem,
  BuildOptions,
  BuildResult,
  BuildStats,
  BuildHistoryItem,
  RollbackOptions,
  RollbackInfo,
  RollbackValidation,
  RollbackResult,
  RollbackRecord,
  CheckpointResult,
  ManifestOptions,
  PatchOptions,
  VerifyOptions,
  CleanOptions,
  CompareOptions,
  HotUpdateResult,
  DeployResult,
  DeploymentRecord,
  DeploymentStatus,
  ConfigValidation,
  ConfigExportOptions,
  ConfigImportOptions
} from '../types/api';

/**
 * 统一的 API 客户端类
 * 封装所有 IPC 通信，提供类型安全的接口
 */
export class VersionCraftAPIClient {
  private static instance: VersionCraftAPIClient;

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): VersionCraftAPIClient {
    if (!VersionCraftAPIClient.instance) {
      VersionCraftAPIClient.instance = new VersionCraftAPIClient();
    }
    return VersionCraftAPIClient.instance;
  }

  // ==================== 版本管理 API ====================

  /**
   * 版本管理相关 API
   */
  version = {
    /**
     * 获取当前版本信息
     */
    getCurrent: (): Promise<APIResponse<VersionInfo>> => 
      this.invoke('version:get-current'),

    /**
     * 升级版本
     */
    bump: (options: BumpOptions): Promise<APIResponse<{ message: string; versionInfo: any }>> => 
      this.invoke('version:bump', options),

    /**
     * 获取版本历史
     */
    getHistory: (): Promise<APIResponse<VersionHistoryItem[]>> => 
      this.invoke('version:get-history'),

    /**
     * 创建 Git 标签
     */
    createTag: (options: TagOptions): Promise<APIResponse<{ message: string; tag: string }>> => 
      this.invoke('version:create-tag', options),

    /**
     * 生成变更日志
     */
    generateChangelog: (options?: ChangelogOptions): Promise<APIResponse<{ message: string; path: string }>> => 
      this.invoke('version:generate-changelog', options),

    /**
     * 从预发布版本发布正式版本
     */
    release: (options?: ReleaseOptions): Promise<APIResponse<{ message: string; versionInfo: any }>> => 
      this.invoke('version:release', options),

    /**
     * 获取格式化的版本列表
     */
    getFormattedList: (): Promise<APIResponse<Array<{ version: string; isCurrent: boolean }>>> => 
      this.invoke('version:get-formatted-list')
  };

  // ==================== 构建管理 API ====================

  /**
   * 构建管理相关 API
   */
  build = {
    /**
     * 开始构建
     */
    start: (platform: string, options?: BuildOptions): Promise<APIResponse<BuildResult>> => 
      this.invoke('build:start', platform, options),

    /**
     * 取消构建
     */
    cancel: (buildId: string): Promise<APIResponse<{ message: string }>> => 
      this.invoke('build:cancel', buildId),

    /**
     * 获取构建统计
     */
    getStats: (): Promise<APIResponse<BuildStats>> => 
      this.invoke('build:get-stats'),

    /**
     * 清理构建输出
     */
    clean: (): Promise<APIResponse<{ message: string }>> => 
      this.invoke('build:clean'),

    /**
     * 获取构建历史
     */
    getHistory: (): Promise<APIResponse<BuildHistoryItem[]>> => 
      this.invoke('build:get-history'),

    /**
     * 构建所有平台
     */
    buildAll: (options?: BuildOptions): Promise<APIResponse<BuildResult[]>> => 
      this.invoke('build:build-all', options),

    /**
     * 构建 Web 平台
     */
    buildWeb: (options?: BuildOptions): Promise<APIResponse<BuildResult>> => 
      this.invoke('build:build-web', options),

    /**
     * 构建 Android 平台
     */
    buildAndroid: (options?: BuildOptions): Promise<APIResponse<BuildResult>> => 
      this.invoke('build:build-android', options),

    /**
     * 构建 iOS 平台
     */
    buildIOS: (options?: BuildOptions): Promise<APIResponse<BuildResult>> => 
      this.invoke('build:build-ios', options),

    /**
     * 构建 Windows 平台
     */
    buildWindows: (options?: BuildOptions): Promise<APIResponse<BuildResult>> => 
      this.invoke('build:build-windows', options),

    /**
     * 构建 Mac 平台
     */
    buildMac: (options?: BuildOptions): Promise<APIResponse<BuildResult>> => 
      this.invoke('build:build-mac', options)
  };

  // ==================== 回滚管理 API ====================

  /**
   * 回滚管理相关 API
   */
  rollback = {
    /**
     * 获取可回滚版本列表
     */
    list: (): Promise<APIResponse<RollbackInfo[]>> => {
      console.log('🔍 [APIClient] Calling rollback:list...');
      return this.invoke('rollback:list');
    },

    /**
     * 回滚到指定版本
     */
    rollbackTo: (version: string, options?: RollbackOptions): Promise<APIResponse<RollbackResult>> => 
      this.invoke('rollback:rollback-to', version, options),

    /**
     * 回滚到上一个版本
     */
    rollbackLast: (options?: RollbackOptions): Promise<APIResponse<RollbackResult>> => 
      this.invoke('rollback:rollback-last', options),

    /**
     * 获取回滚状态和历史
     */
    getStatus: (): Promise<APIResponse<RollbackRecord[]>> => 
      this.invoke('rollback:get-status'),

    /**
     * 验证回滚可行性
     */
    validate: (version: string): Promise<APIResponse<RollbackValidation>> => 
      this.invoke('rollback:validate', version),

    /**
     * 创建回滚检查点
     */
    createCheckpoint: (name?: string): Promise<APIResponse<CheckpointResult>> => 
      this.invoke('rollback:create-checkpoint', name)
  };

  // ==================== 热更新管理 API ====================

  /**
   * 热更新管理相关 API
   */
  hotupdate = {
    /**
     * 生成资源清单
     */
    generateManifest: (options?: ManifestOptions): Promise<APIResponse<HotUpdateResult>> => 
      this.invoke('hotupdate:generate-manifest', options),

    /**
     * 创建增量更新包
     */
    createPatch: (options: PatchOptions): Promise<APIResponse<HotUpdateResult>> => 
      this.invoke('hotupdate:create-patch', options),

    /**
     * 验证资源清单
     */
    verify: (options: VerifyOptions): Promise<APIResponse<HotUpdateResult>> => 
      this.invoke('hotupdate:verify', options),

    /**
     * 清理旧版本
     */
    clean: (options?: CleanOptions): Promise<APIResponse<HotUpdateResult>> => 
      this.invoke('hotupdate:clean', options),

    /**
     * 比较版本差异
     */
    compare: (options: CompareOptions): Promise<APIResponse<HotUpdateResult>> => 
      this.invoke('hotupdate:compare', options),

    /**
     * 获取热更新配置
     */
    getConfig: (): Promise<APIResponse<any>> => 
      this.invoke('hotupdate:get-config'),

    /**
     * 检查资源变更
     */
    checkChanges: (fromVersion: string, toVersion: string): Promise<APIResponse<HotUpdateResult>> => 
      this.invoke('hotupdate:check-changes', fromVersion, toVersion)
  };

  // ==================== 部署管理 API ====================

  /**
   * 部署管理相关 API
   */
  deploy = {
    /**
     * 部署到测试环境
     */
    toStaging: (platform?: string): Promise<APIResponse<DeployResult>> => 
      this.invoke('deploy:to-staging', platform),

    /**
     * 部署到生产环境
     */
    toProduction: (platform?: string): Promise<APIResponse<DeployResult>> => 
      this.invoke('deploy:to-production', platform),

    /**
     * 获取部署历史
     */
    getHistory: (): Promise<APIResponse<DeploymentRecord[]>> => 
      this.invoke('deploy:get-history'),

    /**
     * 获取部署状态
     */
    getStatus: (): Promise<APIResponse<DeploymentStatus>> => 
      this.invoke('deploy:get-status'),

    /**
     * 回滚部署
     */
    rollback: (deploymentId: string): Promise<APIResponse<{ message: string }>> => 
      this.invoke('deploy:rollback', deploymentId),

    /**
     * 取消部署
     */
    cancel: (deploymentId: string): Promise<APIResponse<{ message: string }>> => 
      this.invoke('deploy:cancel', deploymentId)
  };

  // ==================== 配置管理 API ====================

  /**
   * 配置管理相关 API
   */
  config = {
    /**
     * 获取配置
     */
    get: (): Promise<APIResponse<any>> => 
      this.invoke('config:get'),

    /**
     * 设置配置项
     */
    set: (key: string, value: any): Promise<APIResponse<{ message: string }>> => 
      this.invoke('config:set', key, value),

    /**
     * 验证配置
     */
    validate: (): Promise<APIResponse<ConfigValidation>> => 
      this.invoke('config:validate'),

    /**
     * 重置配置
     */
    reset: (): Promise<APIResponse<{ message: string }>> => 
      this.invoke('config:reset'),

    /**
     * 导出配置
     */
    export: (options: ConfigExportOptions): Promise<APIResponse<{ message: string; path: string }>> => 
      this.invoke('config:export', options),

    /**
     * 导入配置
     */
    import: (options: ConfigImportOptions): Promise<APIResponse<{ message: string }>> => 
      this.invoke('config:import', options),

    /**
     * 初始化配置
     */
    init: (): Promise<APIResponse<{ message: string }>> => 
      this.invoke('config:init')
  };

  // ==================== 私有方法 ====================

  /**
   * 统一的 IPC 调用方法
   */
  private async invoke<T = any>(channel: string, ...args: any[]): Promise<APIResponse<T>> {
    try {
      // 检查是否在渲染进程且 electronAPI 可用
      if (!isRendererProcess || !safeWindow?.electronAPI || typeof safeWindow.electronAPI.invoke !== 'function') {
        throw new Error('Electron API 不可用');
      }

      // 调用 IPC
      const result = await safeWindow.electronAPI.invoke(channel, ...args);
      
      // 确保返回格式符合 APIResponse
      if (typeof result === 'object' && result !== null) {
        return {
          success: result.success ?? true,
          data: result.data ?? result,
          error: result.error,
          code: result.code,
          timestamp: result.timestamp ?? new Date().toISOString()
        };
      }

      // 兼容旧格式
      return {
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      // 创建统一的错误对象
      const apiError = new (APIError as any)(channel, error);
      
      // 返回错误响应
      return {
        success: false,
        error: apiError.message,
        code: apiError.code,
        timestamp: new Date().toISOString()
      };
    }
  }

  // ==================== 系统管理 API ====================

  /**
   * 系统管理相关 API
   */
  system = {
    /**
     * 健康检查
     */
    healthCheck: (): Promise<APIResponse<{
      version: boolean;
      build: boolean;
      config: boolean;
      deploy: boolean;
      hotupdate: boolean;
      rollback: boolean;
      overall: boolean;
    }>> => this.invoke('system:health-check'),

    /**
     * 获取服务信息
     */
    getServiceInfo: (): Promise<APIResponse<any>> =>
      this.invoke('system:get-service-info'),

    /**
     * 获取项目信息
     */
    getProjectInfo: (): Promise<APIResponse<any>> =>
      this.invoke('system:get-project-info'),

    /**
     * 设置项目路径
     */
    setProjectPath: (path: string): Promise<APIResponse<{ message: string }>> =>
      this.invoke('system:set-project-path', path),

    /**
     * 获取日志
     */
    getLogs: (level?: string, limit?: number): Promise<APIResponse<any[]>> =>
      this.invoke('system:get-logs', level, limit)
  };

  // ==================== 事件监听 API ====================

  /**
   * 事件监听相关 API
   */
  events = {
    /**
     * 监听构建进度
     */
    onBuildProgress: (callback: (data: any) => void) => {
      if (isRendererProcess && safeWindow?.electronAPI && safeWindow.electronAPI.on) {
        safeWindow.electronAPI.on('build:progress', callback);
      }
    },

    /**
     * 监听部署进度
     */
    onDeployProgress: (callback: (data: any) => void) => {
      if (isRendererProcess && safeWindow?.electronAPI && safeWindow.electronAPI.on) {
        safeWindow.electronAPI.on('deploy:progress', callback);
      }
    },

    /**
     * 监听版本变更
     */
    onVersionChanged: (callback: () => void) => {
      if (isRendererProcess && safeWindow?.electronAPI && safeWindow.electronAPI.on) {
        safeWindow.electronAPI.on('version-changed', callback);
      }
    },

    /**
     * 监听配置变更
     */
    onConfigChanged: (callback: () => void) => {
      if (isRendererProcess && safeWindow?.electronAPI && safeWindow.electronAPI.on) {
        safeWindow.electronAPI.on('config-changed', callback);
      }
    },

    /**
     * 取消事件监听
     */
    off: (channel: string, callback?: Function) => {
      if (isRendererProcess && safeWindow?.electronAPI && safeWindow.electronAPI.off) {
        safeWindow.electronAPI.off(channel, callback);
      }
    }
  };
}

// 导出单例实例
export const apiClient = VersionCraftAPIClient.getInstance();

// 导出类型
export type { APIResponse, APIError };
