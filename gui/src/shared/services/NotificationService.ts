/**
 * 统一通知服务
 * 提供全局的消息通知功能
 */

import { ref } from 'vue';
import { isBrowser, focusWindow } from '../utils/environment';
import type { NotificationConfig, NotificationAction } from './ErrorHandler';

// ==================== 通知类型定义 ====================

export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration: number;
  timestamp: number;
  actions?: NotificationAction[];
  persistent?: boolean;
  progress?: number;
}

export interface NotificationOptions {
  title?: string;
  message: string;
  duration?: number;
  actions?: NotificationAction[];
  persistent?: boolean;
  progress?: number;
}

export interface ToastOptions extends NotificationOptions {
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';
  showClose?: boolean;
  icon?: string;
}

// ==================== 通知服务类 ====================

export class NotificationService {
  private static notifications = ref<Notification[]>([]);
  private static maxNotifications = 5;
  private static defaultDuration = 5000;

  /**
   * 显示通知
   */
  static show(config: NotificationConfig): string {
    const notification: Notification = {
      id: this.generateId(),
      type: config.type,
      title: config.title,
      message: config.message,
      duration: config.duration ?? this.defaultDuration,
      timestamp: Date.now(),
      actions: config.actions,
      persistent: config.duration === 0
    };
    
    // 添加到通知列表
    this.notifications.value.push(notification);
    
    // 限制通知数量
    if (this.notifications.value.length > this.maxNotifications) {
      this.notifications.value.shift();
    }
    
    // 设置自动移除
    if (notification.duration > 0) {
      setTimeout(() => {
        this.remove(notification.id);
      }, notification.duration);
    }
    
    // 触发系统通知（如果支持）
    this.showSystemNotification(notification);
    
    return notification.id;
  }

  /**
   * 成功通知
   */
  static success(message: string, options: Partial<NotificationOptions> = {}): string {
    return this.show({
      type: 'success',
      title: options.title || '操作成功',
      message,
      duration: options.duration ?? 3000,
      actions: options.actions
    });
  }

  /**
   * 错误通知
   */
  static error(message: string, options: Partial<NotificationOptions> = {}): string {
    return this.show({
      type: 'error',
      title: options.title || '操作失败',
      message,
      duration: options.duration ?? 5000,
      actions: options.actions
    });
  }

  /**
   * 警告通知
   */
  static warning(message: string, options: Partial<NotificationOptions> = {}): string {
    return this.show({
      type: 'warning',
      title: options.title || '警告',
      message,
      duration: options.duration ?? 4000,
      actions: options.actions
    });
  }

  /**
   * 信息通知
   */
  static info(message: string, options: Partial<NotificationOptions> = {}): string {
    return this.show({
      type: 'info',
      title: options.title || '提示',
      message,
      duration: options.duration ?? 3000,
      actions: options.actions
    });
  }

  /**
   * 进度通知
   */
  static progress(
    message: string, 
    progress: number, 
    options: Partial<NotificationOptions> = {}
  ): string {
    const existingId = options.title ? this.findByTitle(options.title) : null;
    
    if (existingId) {
      // 更新现有进度通知
      this.updateProgress(existingId, progress, message);
      return existingId;
    } else {
      // 创建新的进度通知
      return this.show({
        type: 'info',
        title: options.title || '进度',
        message,
        duration: 0, // 进度通知不自动消失
        actions: options.actions
      });
    }
  }

  /**
   * 移除通知
   */
  static remove(id: string): void {
    const index = this.notifications.value.findIndex(n => n.id === id);
    if (index > -1) {
      this.notifications.value.splice(index, 1);
    }
  }

  /**
   * 移除所有通知
   */
  static clear(): void {
    this.notifications.value.splice(0);
  }

  /**
   * 更新进度
   */
  static updateProgress(id: string, progress: number, message?: string): void {
    const notification = this.notifications.value.find(n => n.id === id);
    if (notification) {
      notification.progress = Math.max(0, Math.min(100, progress));
      if (message) {
        notification.message = message;
      }
      
      // 如果进度达到100%，自动移除
      if (progress >= 100) {
        setTimeout(() => this.remove(id), 2000);
      }
    }
  }

  /**
   * 获取所有通知
   */
  static getNotifications() {
    return this.notifications;
  }

  /**
   * 获取通知数量
   */
  static getCount(): number {
    return this.notifications.value.length;
  }

  /**
   * 检查是否有错误通知
   */
  static hasErrors(): boolean {
    return this.notifications.value.some(n => n.type === 'error');
  }

  /**
   * 获取最新的错误通知
   */
  static getLatestError(): Notification | null {
    const errors = this.notifications.value.filter(n => n.type === 'error');
    return errors.length > 0 ? errors[errors.length - 1] : null;
  }

  // ==================== 私有方法 ====================

  /**
   * 生成唯一ID
   */
  private static generateId(): string {
    return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 根据标题查找通知
   */
  private static findByTitle(title: string): string | null {
    const notification = this.notifications.value.find(n => n.title === title);
    return notification ? notification.id : null;
  }

  /**
   * 显示系统通知
   */
  private static showSystemNotification(notification: Notification): void {
    // 检查是否支持系统通知
    if (!('Notification' in window)) {
      return;
    }

    // 检查权限
    if (Notification.permission === 'granted') {
      this.createSystemNotification(notification);
    } else if (Notification.permission !== 'denied') {
      // 请求权限
      Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
          this.createSystemNotification(notification);
        }
      });
    }
  }

  /**
   * 创建系统通知
   */
  private static createSystemNotification(notification: Notification): void {
    try {
      const systemNotification = new Notification(notification.title, {
        body: notification.message,
        icon: this.getNotificationIcon(notification.type),
        tag: notification.id,
        requireInteraction: notification.persistent
      });

      // 点击事件
      systemNotification.onclick = () => {
        // 聚焦到应用窗口
        window.focus();
        systemNotification.close();
      };

      // 自动关闭
      if (notification.duration > 0) {
        setTimeout(() => {
          systemNotification.close();
        }, notification.duration);
      }
    } catch (error) {
      console.warn('Failed to create system notification:', error);
    }
  }

  /**
   * 获取通知图标
   */
  private static getNotificationIcon(type: string): string {
    const icons = {
      success: '/icons/success.png',
      error: '/icons/error.png',
      warning: '/icons/warning.png',
      info: '/icons/info.png'
    };
    
    return icons[type as keyof typeof icons] || icons.info;
  }

  // ==================== 配置方法 ====================

  /**
   * 设置最大通知数量
   */
  static setMaxNotifications(max: number): void {
    this.maxNotifications = Math.max(1, max);
  }

  /**
   * 设置默认持续时间
   */
  static setDefaultDuration(duration: number): void {
    this.defaultDuration = Math.max(0, duration);
  }

  /**
   * 请求系统通知权限
   */
  static async requestPermission(): Promise<NotificationPermission> {
    if (!('Notification' in window)) {
      return 'denied';
    }

    if (Notification.permission === 'default') {
      return await Notification.requestPermission();
    }

    return Notification.permission;
  }
}

// ==================== Toast 服务 ====================

export class ToastService {
  private static toasts = ref<Array<Notification & { position: string }>>([]);

  /**
   * 显示 Toast
   */
  static show(options: ToastOptions): string {
    const toast = {
      id: NotificationService['generateId'](),
      type: 'info' as const,
      title: options.title || '',
      message: options.message,
      duration: options.duration ?? 3000,
      timestamp: Date.now(),
      position: options.position || 'top-right',
      actions: options.actions
    };

    this.toasts.value.push(toast);

    // 自动移除
    if (toast.duration > 0) {
      setTimeout(() => {
        this.remove(toast.id);
      }, toast.duration);
    }

    return toast.id;
  }

  /**
   * 移除 Toast
   */
  static remove(id: string): void {
    const index = this.toasts.value.findIndex(t => t.id === id);
    if (index > -1) {
      this.toasts.value.splice(index, 1);
    }
  }

  /**
   * 获取所有 Toast
   */
  static getToasts() {
    return this.toasts;
  }

  /**
   * 清除所有 Toast
   */
  static clear(): void {
    this.toasts.value.splice(0);
  }
}

// 导出响应式数据供组件使用
export const notifications = NotificationService.getNotifications();
export const toasts = ToastService.getToasts();
