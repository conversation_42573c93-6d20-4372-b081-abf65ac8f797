<!--
  通用文本域组件
  支持自动调整高度、字符计数、验证等
-->

<template>
  <div class="vc-textarea" :class="{ 'textarea-disabled': disabled }">
    <label v-if="label" class="textarea-label" :for="textareaId">
      {{ label }}
      <span v-if="required" class="required-mark">*</span>
    </label>
    
    <div class="textarea-container">
      <textarea
        :id="textareaId"
        ref="textareaRef"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :rows="rows"
        :maxlength="maxlength"
        :minlength="minlength"
        :class="[
          'textarea-field',
          {
            'textarea-error': error,
            'textarea-success': success,
            'textarea-resizable': resizable
          }
        ]"
        @input="handleInput"
        @change="handleChange"
        @focus="handleFocus"
        @blur="handleBlur"
        @keydown="handleKeydown"
      ></textarea>
      
      <!-- 清除按钮 -->
      <button
        v-if="clearable && modelValue && !disabled && !readonly"
        type="button"
        class="textarea-clear-btn"
        @click="clearTextarea"
      >
        <XMarkIcon class="clear-icon" />
      </button>
    </div>

    <!-- 字符计数 -->
    <div v-if="showCount" class="textarea-count">
      <span :class="{ 'count-over': isOverLimit }">
        {{ currentLength }}
      </span>
      <span v-if="maxlength" class="count-separator">/</span>
      <span v-if="maxlength" class="count-max">{{ maxlength }}</span>
    </div>

    <!-- 错误信息 -->
    <div v-if="error" class="textarea-error-text">
      <ExclamationCircleIcon class="error-icon" />
      {{ error }}
    </div>

    <!-- 成功信息 -->
    <div v-if="success" class="textarea-success-text">
      <CheckCircleIcon class="success-icon" />
      {{ success }}
    </div>

    <!-- 帮助文本 -->
    <div v-if="help && !error && !success" class="textarea-help-text">
      {{ help }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch } from 'vue';
import {
  XMarkIcon,
  ExclamationCircleIcon,
  CheckCircleIcon
} from '@heroicons/vue/24/outline';

// Props
interface Props {
  modelValue: string | null;
  label?: string;
  placeholder?: string;
  error?: string;
  success?: string;
  help?: string;
  disabled?: boolean;
  readonly?: boolean;
  required?: boolean;
  clearable?: boolean;
  showCount?: boolean;
  autoResize?: boolean;
  resizable?: boolean;
  rows?: number;
  maxlength?: number;
  minlength?: number;
  minRows?: number;
  maxRows?: number;
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  readonly: false,
  required: false,
  clearable: false,
  showCount: false,
  autoResize: false,
  resizable: true,
  rows: 3,
  minRows: 2,
  maxRows: 10
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string | null];
  change: [value: string | null];
  focus: [event: FocusEvent];
  blur: [event: FocusEvent];
  keydown: [event: KeyboardEvent];
  clear: [];
}>();

// 响应式数据
const textareaRef = ref<HTMLTextAreaElement>();
const isFocused = ref(false);

// 计算属性
const textareaId = computed(() => {
  return `textarea-${Math.random().toString(36).substr(2, 9)}`;
});

const currentLength = computed(() => {
  return (props.modelValue || '').length;
});

const isOverLimit = computed(() => {
  return props.maxlength ? currentLength.value > props.maxlength : false;
});

// 监听器
watch(() => props.modelValue, () => {
  if (props.autoResize) {
    nextTick(() => {
      adjustHeight();
    });
  }
}, { immediate: true });

// 方法
const handleInput = (event: Event) => {
  const target = event.target as HTMLTextAreaElement;
  emit('update:modelValue', target.value);
  
  if (props.autoResize) {
    adjustHeight();
  }
};

const handleChange = (event: Event) => {
  const target = event.target as HTMLTextAreaElement;
  emit('change', target.value);
};

const handleFocus = (event: FocusEvent) => {
  isFocused.value = true;
  emit('focus', event);
};

const handleBlur = (event: FocusEvent) => {
  isFocused.value = false;
  emit('blur', event);
};

const handleKeydown = (event: KeyboardEvent) => {
  emit('keydown', event);
};

const clearTextarea = () => {
  emit('update:modelValue', '');
  emit('clear');
  nextTick(() => {
    textareaRef.value?.focus();
    if (props.autoResize) {
      adjustHeight();
    }
  });
};

const adjustHeight = () => {
  const textarea = textareaRef.value;
  if (!textarea) return;
  
  // 重置高度以获取正确的 scrollHeight
  textarea.style.height = 'auto';
  
  // 计算新高度
  const lineHeight = parseInt(getComputedStyle(textarea).lineHeight);
  const minHeight = lineHeight * props.minRows;
  const maxHeight = lineHeight * props.maxRows;
  
  let newHeight = Math.max(textarea.scrollHeight, minHeight);
  if (maxHeight > 0) {
    newHeight = Math.min(newHeight, maxHeight);
  }
  
  textarea.style.height = `${newHeight}px`;
};

const focus = () => {
  textareaRef.value?.focus();
};

const blur = () => {
  textareaRef.value?.blur();
};

const select = () => {
  textareaRef.value?.select();
};

// 暴露方法
defineExpose({
  focus,
  blur,
  select,
  adjustHeight
});
</script>

<style scoped>
/* 文本域容器 */
.vc-textarea {
  @apply relative;
}

.textarea-disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* 标签 */
.textarea-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.required-mark {
  @apply text-red-500 ml-1;
}

/* 文本域容器 */
.textarea-container {
  @apply relative;
}

/* 文本域 */
.textarea-field {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md text-gray-900 placeholder-gray-500 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:cursor-not-allowed readonly:bg-gray-50 readonly:cursor-default;
}

.textarea-field:hover:not(:disabled):not(:readonly) {
  @apply border-gray-400;
}

.textarea-error {
  @apply border-red-500 focus:ring-red-500 focus:border-red-500;
}

.textarea-success {
  @apply border-green-500 focus:ring-green-500 focus:border-green-500;
}

.textarea-resizable {
  @apply resize-y;
}

.textarea-field:not(.textarea-resizable) {
  @apply resize-none;
}

/* 清除按钮 */
.textarea-clear-btn {
  @apply absolute top-2 right-2 text-gray-400 hover:text-gray-600 transition-colors p-1 rounded;
}

.clear-icon {
  @apply w-4 h-4;
}

/* 字符计数 */
.textarea-count {
  @apply mt-1 text-xs text-right;
}

.textarea-count {
  @apply text-gray-500;
}

.count-over {
  @apply text-red-500 font-medium;
}

.count-separator {
  @apply mx-1;
}

.count-max {
  @apply text-gray-400;
}

/* 错误信息 */
.textarea-error-text {
  @apply mt-1 flex items-center text-sm text-red-600;
}

.error-icon {
  @apply w-4 h-4 mr-1 flex-shrink-0;
}

/* 成功信息 */
.textarea-success-text {
  @apply mt-1 flex items-center text-sm text-green-600;
}

.success-icon {
  @apply w-4 h-4 mr-1 flex-shrink-0;
}

/* 帮助文本 */
.textarea-help-text {
  @apply mt-1 text-sm text-gray-500;
}

/* 滚动条样式 */
.textarea-field::-webkit-scrollbar {
  @apply w-2;
}

.textarea-field::-webkit-scrollbar-track {
  @apply bg-gray-100 rounded;
}

.textarea-field::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded hover:bg-gray-400;
}

/* Firefox 滚动条 */
.textarea-field {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f3f4f6;
}
</style>
