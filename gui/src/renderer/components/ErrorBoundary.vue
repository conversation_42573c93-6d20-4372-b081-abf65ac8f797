<!--
  Vue 错误边界组件
  捕获子组件中的错误并提供友好的错误界面
-->

<template>
  <div v-if="hasError" class="error-boundary">
    <div class="error-content">
      <!-- 错误图标 -->
      <div class="error-icon">
        <svg class="w-16 h-16 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      </div>

      <!-- 错误标题 -->
      <h2 class="error-title">
        😵 出现了意外错误
      </h2>

      <!-- 错误消息 -->
      <div class="error-message">
        <p class="main-message">{{ errorMessage }}</p>
        <details v-if="showDetails" class="error-details">
          <summary class="details-summary">查看详细信息</summary>
          <div class="details-content">
            <div class="error-info">
              <div class="info-item">
                <span class="info-label">错误类型:</span>
                <span class="info-value">{{ errorInfo.name || 'Unknown' }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">发生时间:</span>
                <span class="info-value">{{ errorInfo.timestamp }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">组件路径:</span>
                <span class="info-value">{{ errorInfo.componentName || 'Unknown' }}</span>
              </div>
            </div>
            
            <div v-if="errorInfo.stack" class="stack-trace">
              <h4 class="stack-title">堆栈跟踪:</h4>
              <pre class="stack-content">{{ errorInfo.stack }}</pre>
            </div>
          </div>
        </details>
      </div>

      <!-- 操作按钮 -->
      <div class="error-actions">
        <button @click="retry" class="btn btn-primary">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          重试
        </button>
        
        <button @click="reportError" class="btn btn-secondary">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          报告问题
        </button>
        
        <button @click="goHome" class="btn btn-outline">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
          </svg>
          返回首页
        </button>
        
        <button @click="toggleDetails" class="btn btn-ghost">
          {{ showDetails ? '隐藏详情' : '显示详情' }}
        </button>
      </div>

      <!-- 建议信息 -->
      <div class="error-suggestions">
        <h3 class="suggestions-title">可能的解决方案:</h3>
        <ul class="suggestions-list">
          <li>刷新页面重新加载应用</li>
          <li>检查网络连接是否正常</li>
          <li>清除浏览器缓存后重试</li>
          <li>如果问题持续存在，请联系技术支持</li>
        </ul>
      </div>
    </div>
  </div>
  
  <!-- 正常内容 -->
  <slot v-else />
</template>

<script setup lang="ts">
import { ref, onErrorCaptured, getCurrentInstance } from 'vue';
import { useRouter } from 'vue-router';
import { ErrorHandler } from '@/shared/services/ErrorHandler';
import { NotificationService } from '@/shared/services/NotificationService';

// Props
interface Props {
  fallbackComponent?: any;
  onError?: (error: Error, instance: any, info: string) => void;
  showDetails?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  showDetails: true
});

// Emits
const emit = defineEmits<{
  error: [error: Error, instance: any, info: string];
  retry: [];
}>();

// 响应式数据
const hasError = ref(false);
const errorMessage = ref('');
const errorInfo = ref<{
  name?: string;
  message?: string;
  stack?: string;
  timestamp?: string;
  componentName?: string;
}>({});
const lastError = ref<Error | null>(null);
const showDetails = ref(props.showDetails);

// 路由
const router = useRouter();
const instance = getCurrentInstance();

// 错误捕获
onErrorCaptured((error: Error, instance: any, info: string) => {
  console.error('ErrorBoundary caught error:', error);
  
  // 设置错误状态
  hasError.value = true;
  errorMessage.value = error.message || '发生了未知错误';
  lastError.value = error;
  
  // 收集错误信息
  errorInfo.value = {
    name: error.name,
    message: error.message,
    stack: error.stack,
    timestamp: new Date().toLocaleString(),
    componentName: instance?.$options.name || instance?.$options.__name || 'Unknown'
  };
  
  // 调用错误处理器
  ErrorHandler.handle(error, 'Vue Component Error', {
    componentName: errorInfo.value.componentName,
    errorInfo: info
  });
  
  // 触发事件
  emit('error', error, instance, info);
  if (props.onError) {
    props.onError(error, instance, info);
  }
  
  // 阻止错误继续传播
  return false;
});

// 方法
const retry = () => {
  hasError.value = false;
  errorMessage.value = '';
  errorInfo.value = {};
  lastError.value = null;
  
  emit('retry');
  
  // 强制重新渲染
  if (instance) {
    instance.proxy?.$forceUpdate();
  }
};

const reportError = () => {
  if (lastError.value) {
    // 创建错误报告
    const errorReport = {
      error: {
        name: lastError.value.name,
        message: lastError.value.message,
        stack: lastError.value.stack
      },
      errorInfo: errorInfo.value,
      userAgent: navigator.userAgent,
      url: window.location.href,
      timestamp: new Date().toISOString()
    };
    
    // 复制到剪贴板
    navigator.clipboard.writeText(JSON.stringify(errorReport, null, 2)).then(() => {
      NotificationService.success('错误信息已复制到剪贴板，请发送给技术支持');
    }).catch(() => {
      NotificationService.error('复制失败，请手动复制错误信息');
    });
  }
};

const goHome = () => {
  hasError.value = false;
  router.push('/').catch(console.error);
};

const toggleDetails = () => {
  showDetails.value = !showDetails.value;
};

// 重置错误状态的方法（供外部调用）
const resetError = () => {
  hasError.value = false;
  errorMessage.value = '';
  errorInfo.value = {};
  lastError.value = null;
};

// 暴露方法给父组件
defineExpose({
  resetError,
  hasError: () => hasError.value,
  getLastError: () => lastError.value
});
</script>

<style scoped>
.error-boundary {
  @apply min-h-screen bg-gray-50 flex items-center justify-center p-4;
}

.error-content {
  @apply max-w-2xl w-full bg-white rounded-lg shadow-lg p-8 text-center;
}

.error-icon {
  @apply flex justify-center mb-6;
}

.error-title {
  @apply text-2xl font-bold text-gray-900 mb-4;
}

.error-message {
  @apply text-left mb-6;
}

.main-message {
  @apply text-gray-700 mb-4 p-4 bg-red-50 border border-red-200 rounded-md;
}

.error-details {
  @apply mt-4;
}

.details-summary {
  @apply cursor-pointer text-blue-600 hover:text-blue-800 font-medium;
}

.details-content {
  @apply mt-4 p-4 bg-gray-50 rounded-md text-sm;
}

.error-info {
  @apply space-y-2 mb-4;
}

.info-item {
  @apply flex justify-between;
}

.info-label {
  @apply font-medium text-gray-600;
}

.info-value {
  @apply text-gray-900 font-mono;
}

.stack-trace {
  @apply mt-4;
}

.stack-title {
  @apply font-medium text-gray-700 mb-2;
}

.stack-content {
  @apply bg-gray-900 text-green-400 p-3 rounded text-xs overflow-x-auto;
}

.error-actions {
  @apply flex flex-wrap gap-3 justify-center mb-6;
}

.btn {
  @apply inline-flex items-center px-4 py-2 rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
}

.btn-outline {
  @apply border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-500;
}

.btn-ghost {
  @apply text-gray-600 hover:text-gray-800 hover:bg-gray-100 focus:ring-gray-500;
}

.error-suggestions {
  @apply text-left bg-blue-50 border border-blue-200 rounded-md p-4;
}

.suggestions-title {
  @apply font-medium text-blue-900 mb-2;
}

.suggestions-list {
  @apply text-blue-800 text-sm space-y-1;
}

.suggestions-list li {
  @apply flex items-start;
}

.suggestions-list li::before {
  content: "•";
  @apply text-blue-600 font-bold mr-2 mt-0.5;
}
</style>
