<!--
  变更日志模态框
  用于查看、编辑和生成变更日志
-->

<template>
  <VCModal 
    v-model="visible" 
    title="变更日志管理" 
    :loading="isLoading"
    size="extra-large"
    @cancel="handleCancel"
  >
    <div class="changelog-management">
      <!-- 工具栏 -->
      <div class="changelog-toolbar">
        <div class="toolbar-left">
          <div class="changelog-info">
            <span v-if="lastModified" class="info-item">
              最后更新: {{ formatDate(lastModified) }}
            </span>
            <span v-if="changelogContent" class="info-item">
              {{ getContentStats() }}
            </span>
          </div>
        </div>
        <div class="toolbar-right">
          <button 
            @click="generateChangelog" 
            :disabled="isGenerating"
            class="btn btn-secondary btn-sm"
          >
            <DocumentPlusIcon class="w-4 h-4 mr-2" />
            {{ isGenerating ? '生成中...' : '重新生成' }}
          </button>
          <button 
            @click="toggleEditMode" 
            class="btn btn-secondary btn-sm"
          >
            <component :is="isEditing ? EyeIcon : PencilIcon" class="w-4 h-4 mr-2" />
            {{ isEditing ? '预览' : '编辑' }}
          </button>
          <button 
            @click="downloadChangelog" 
            :disabled="!changelogContent"
            class="btn btn-secondary btn-sm"
          >
            <ArrowDownTrayIcon class="w-4 h-4 mr-2" />
            下载
          </button>
        </div>
      </div>

      <!-- 生成选项 -->
      <div v-if="showGenerateOptions" class="generate-options">
        <h4 class="options-title">生成选项</h4>
        <div class="options-grid">
          <div class="option-group">
            <label class="option-label">版本范围</label>
            <div class="version-range">
              <VCSelect 
                v-model="generateOptions.fromVersion"
                :options="versionOptions"
                placeholder="起始版本"
                class="version-select"
              />
              <span class="range-separator">至</span>
              <VCSelect 
                v-model="generateOptions.toVersion"
                :options="versionOptions"
                placeholder="结束版本"
                class="version-select"
              />
            </div>
          </div>
          
          <div class="option-group">
            <label class="option-label">包含内容</label>
            <div class="option-checkboxes">
              <label class="checkbox-item">
                <input 
                  type="checkbox" 
                  v-model="generateOptions.includeCommits"
                  class="checkbox-input"
                />
                <span class="checkbox-label">提交信息</span>
              </label>
              <label class="checkbox-item">
                <input 
                  type="checkbox" 
                  v-model="generateOptions.includeMerges"
                  class="checkbox-input"
                />
                <span class="checkbox-label">合并记录</span>
              </label>
              <label class="checkbox-item">
                <input 
                  type="checkbox" 
                  v-model="generateOptions.includeAuthors"
                  class="checkbox-input"
                />
                <span class="checkbox-label">作者信息</span>
              </label>
            </div>
          </div>
          
          <div class="option-group">
            <label class="option-label">格式选项</label>
            <div class="format-options">
              <select 
                v-model="generateOptions.format"
                class="format-select"
              >
                <option value="markdown">Markdown</option>
                <option value="text">纯文本</option>
                <option value="json">JSON</option>
              </select>
            </div>
          </div>
        </div>
        
        <div class="options-actions">
          <button 
            @click="executeGenerate" 
            :disabled="isGenerating"
            class="btn btn-primary"
          >
            {{ isGenerating ? '生成中...' : '开始生成' }}
          </button>
          <button 
            @click="showGenerateOptions = false" 
            class="btn btn-secondary"
          >
            取消
          </button>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="changelog-content">
        <!-- 编辑模式 -->
        <div v-if="isEditing" class="edit-mode">
          <div class="editor-toolbar">
            <div class="editor-actions">
              <button 
                @click="insertTemplate" 
                class="btn btn-sm btn-ghost"
              >
                <DocumentIcon class="w-4 h-4 mr-1" />
                插入模板
              </button>
              <button 
                @click="formatContent" 
                class="btn btn-sm btn-ghost"
              >
                <SparklesIcon class="w-4 h-4 mr-1" />
                格式化
              </button>
            </div>
          </div>
          <VCTextarea 
            v-model="editableContent"
            placeholder="请输入变更日志内容..."
            :rows="20"
            :auto-resize="false"
            :show-count="true"
            class="changelog-editor"
          />
        </div>
        
        <!-- 预览模式 -->
        <div v-else class="preview-mode">
          <div v-if="!changelogContent" class="empty-state">
            <DocumentTextIcon class="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p class="text-gray-500 mb-4">暂无变更日志</p>
            <button 
              @click="showGenerateOptions = true" 
              class="btn btn-primary"
            >
              生成变更日志
            </button>
          </div>
          
          <div v-else class="changelog-preview">
            <div class="markdown-content" v-html="renderedContent"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 自定义按钮 -->
    <template #footer>
      <div class="modal-footer">
        <button 
          @click="handleCancel" 
          class="btn btn-secondary"
        >
          关闭
        </button>
        <button 
          v-if="isEditing"
          @click="saveChangelog" 
          :disabled="!hasChanges"
          class="btn btn-primary"
        >
          保存变更
        </button>
      </div>
    </template>
  </VCModal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import {
  DocumentTextIcon,
  DocumentPlusIcon,
  PencilIcon,
  EyeIcon,
  ArrowDownTrayIcon,
  DocumentIcon,
  SparklesIcon
} from '@heroicons/vue/24/outline';

import { apiClient } from '../../../shared/api/VersionCraftAPIClient';
import { ErrorHandler } from '../../../shared/services/ErrorHandler';
import { NotificationService } from '../../../shared/services/NotificationService';

// 导入基础组件
import VCModal from '../ui/VCModal.vue';
import VCSelect from '../ui/VCSelect.vue';
import VCTextarea from '../ui/VCTextarea.vue';

// Props
interface Props {
  modelValue: boolean;
  changelogContent?: string;
  versionHistory?: any[];
}

const props = withDefaults(defineProps<Props>(), {
  changelogContent: '',
  versionHistory: () => []
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  generate: [];
}>();

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const isLoading = ref(false);
const isGenerating = ref(false);
const isEditing = ref(false);
const showGenerateOptions = ref(false);
const editableContent = ref('');
const lastModified = ref('');

const generateOptions = ref({
  fromVersion: '',
  toVersion: '',
  includeCommits: true,
  includeMerges: false,
  includeAuthors: <AUTHORS>
  format: 'markdown'
});

// 计算属性
const changelogContent = computed(() => props.changelogContent);

const versionOptions = computed(() => {
  return props.versionHistory.map(version => ({
    label: `${version.version} (${new Date(version.date).toLocaleDateString()})`,
    value: version.version
  }));
});

const hasChanges = computed(() => {
  return editableContent.value !== changelogContent.value;
});

const renderedContent = computed(() => {
  // 这里应该使用 markdown 渲染器，暂时返回原始内容
  return changelogContent.value.replace(/\n/g, '<br>');
});

// 监听器
watch(() => props.changelogContent, (newContent) => {
  editableContent.value = newContent;
});

watch(visible, (newVisible) => {
  if (newVisible) {
    editableContent.value = changelogContent.value;
    loadChangelogInfo();
  }
});

// 方法
const loadChangelogInfo = async () => {
  try {
    // 使用现有的 API 方法获取版本信息
    const result = await apiClient.version.getCurrent();
    if (result.success && result.data) {
      lastModified.value = result.data.lastModified || new Date().toISOString();
    }
  } catch (error) {
    // 忽略错误，可能是文件不存在
  }
};

const generateChangelog = () => {
  showGenerateOptions.value = true;
};

// generateChangelogFromHistory 函数已移除，因为未使用

const executeGenerate = async () => {
  try {
    isGenerating.value = true;
    const result = await apiClient.version.generateChangelog({
      from: generateOptions.value.fromVersion,
      to: generateOptions.value.toVersion,
      format: generateOptions.value.format as 'markdown' | 'json',
      includeCommits: generateOptions.value.includeCommits
    });

    if (result.success && result.data) {
      // API 返回的是 { message: string; path: string }，我们需要构建变更日志内容
      editableContent.value = `# 变更日志\n\n## 最新更新\n\n${result.data.message}\n\n*生成路径: ${result.data.path}*`;
      lastModified.value = new Date().toISOString();
      showGenerateOptions.value = false;
      NotificationService.success('变更日志生成成功');
      emit('generate');
    } else {
      throw new Error(result.error || '生成变更日志失败');
    }
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Generate Changelog');
  } finally {
    isGenerating.value = false;
  }
};

const toggleEditMode = () => {
  isEditing.value = !isEditing.value;
};

const saveChangelog = async () => {
  try {
    // 使用浏览器下载功能保存变更日志
    const blob = new Blob([editableContent.value], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = 'CHANGELOG.md';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    lastModified.value = new Date().toISOString();
    NotificationService.success('变更日志已保存并下载');
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Save Changelog');
  }
};

const downloadChangelog = () => {
  if (!changelogContent.value) return;
  
  const blob = new Blob([changelogContent.value], { type: 'text/markdown' });
  const url = URL.createObjectURL(blob);
  
  const a = document.createElement('a');
  a.href = url;
  a.download = 'CHANGELOG.md';
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
  
  NotificationService.success('变更日志已下载');
};

const insertTemplate = () => {
  const template = `# 变更日志

## [版本号] - 日期

### 新增
- 新功能描述

### 修改
- 修改内容描述

### 修复
- 修复问题描述

### 删除
- 删除内容描述
`;
  
  editableContent.value = template + '\n\n' + editableContent.value;
};

const formatContent = () => {
  // 简单的格式化逻辑
  editableContent.value = editableContent.value
    .replace(/\n{3,}/g, '\n\n') // 移除多余空行
    .trim();
  
  NotificationService.success('内容格式化完成');
};

const getContentStats = () => {
  if (!changelogContent.value) return '';
  
  const lines = changelogContent.value.split('\n').length;
  const words = changelogContent.value.split(/\s+/).length;
  
  return `${lines} 行, ${words} 词`;
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN');
};

const handleCancel = () => {
  visible.value = false;
  // 重置状态
  isEditing.value = false;
  showGenerateOptions.value = false;
  editableContent.value = '';
};
</script>

<style scoped>
/* 变更日志管理样式 */
.changelog-management {
  @apply space-y-6;
}

/* 工具栏 */
.changelog-toolbar {
  @apply flex items-center justify-between p-4 bg-gray-50 rounded-lg;
}

.toolbar-left {
  @apply flex items-center space-x-4;
}

.changelog-info {
  @apply flex items-center space-x-4 text-sm text-gray-600;
}

.info-item {
  @apply flex items-center;
}

.toolbar-right {
  @apply flex items-center space-x-2;
}

/* 生成选项 */
.generate-options {
  @apply p-4 bg-blue-50 border border-blue-200 rounded-lg space-y-4;
}

.options-title {
  @apply text-lg font-semibold text-blue-900;
}

.options-grid {
  @apply grid grid-cols-1 md:grid-cols-3 gap-4;
}

.option-group {
  @apply space-y-2;
}

.option-label {
  @apply block text-sm font-medium text-gray-700;
}

.version-range {
  @apply flex items-center space-x-2;
}

.version-select {
  @apply flex-1;
}

.range-separator {
  @apply text-sm text-gray-500;
}

.option-checkboxes {
  @apply space-y-2;
}

.checkbox-item {
  @apply flex items-center space-x-2 cursor-pointer;
}

.checkbox-input {
  @apply h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded;
}

.checkbox-label {
  @apply text-sm text-gray-700;
}

.format-options {
  @apply space-y-2;
}

.format-select {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500;
}

.options-actions {
  @apply flex items-center justify-end space-x-3 pt-4 border-t border-blue-200;
}

/* 内容区域 */
.changelog-content {
  @apply min-h-96;
}

/* 编辑模式 */
.edit-mode {
  @apply space-y-4;
}

.editor-toolbar {
  @apply flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-t-lg;
}

.editor-actions {
  @apply flex items-center space-x-2;
}

.changelog-editor {
  @apply w-full border-t-0 rounded-t-none;
}

/* 预览模式 */
.preview-mode {
  @apply min-h-96;
}

.empty-state {
  @apply flex flex-col items-center justify-center py-12 text-center;
}

.changelog-preview {
  @apply p-4 border border-gray-200 rounded-lg bg-white;
}

.markdown-content {
  @apply prose prose-sm max-w-none;
}

.markdown-content h1 {
  @apply text-2xl font-bold text-gray-900 mb-4;
}

.markdown-content h2 {
  @apply text-xl font-semibold text-gray-800 mb-3 mt-6;
}

.markdown-content h3 {
  @apply text-lg font-medium text-gray-700 mb-2 mt-4;
}

.markdown-content ul {
  @apply list-disc list-inside space-y-1 ml-4;
}

.markdown-content li {
  @apply text-gray-600;
}

/* 模态框底部 */
.modal-footer {
  @apply flex items-center justify-end space-x-3;
}

.btn {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
}

.btn-sm {
  @apply px-3 py-1.5 text-xs;
}

.btn-ghost {
  @apply text-gray-600 hover:text-gray-800 hover:bg-gray-100;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .changelog-toolbar {
    @apply flex-col items-start space-y-3;
  }

  .toolbar-left,
  .toolbar-right {
    @apply w-full justify-between;
  }

  .options-grid {
    @apply grid-cols-1;
  }

  .version-range {
    @apply flex-col space-y-2 space-x-0;
  }

  .options-actions {
    @apply flex-col space-y-2 space-x-0;
  }
}
</style>
