<!--
  部署配置模态框
  用于管理部署相关的配置
-->

<template>
  <VCModal 
    v-model="visible" 
    title="部署配置" 
    :loading="isSaving"
    size="large"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="deploy-config">
      <!-- 平台配置 -->
      <div class="platform-configs">
        <h4 class="section-title">平台配置</h4>
        <div class="config-tabs">
          <div class="tabs-nav">
            <button 
              v-for="platform in platforms" 
              :key="platform.key"
              @click="activeTab = platform.key"
              :class="['tab-button', { 'tab-active': activeTab === platform.key }]"
            >
              <component :is="platform.icon" class="w-4 h-4 mr-2" />
              {{ platform.name }}
            </button>
          </div>
          
          <div class="tab-content">
            <div v-for="platform in platforms" :key="platform.key">
              <div v-if="activeTab === platform.key" class="platform-config">
                <div class="config-form">
                  <div class="form-group">
                    <label class="form-label">构建命令</label>
                    <VCInput 
                      v-model="configs[platform.key].buildCommand"
                      placeholder="例如: npm run build"
                    />
                  </div>
                  
                  <div class="form-group">
                    <label class="form-label">输出目录</label>
                    <VCInput 
                      v-model="configs[platform.key].outputDir"
                      placeholder="例如: dist"
                    />
                  </div>
                  
                  <div class="form-group">
                    <label class="form-label">部署脚本</label>
                    <VCTextarea 
                      v-model="configs[platform.key].deployScript"
                      placeholder="输入部署脚本..."
                      :rows="4"
                    />
                  </div>
                  
                  <div class="form-group">
                    <label class="form-label">环境变量</label>
                    <div class="env-vars">
                      <div 
                        v-for="(envVar, index) in configs[platform.key].envVars" 
                        :key="index"
                        class="env-var-item"
                      >
                        <VCInput 
                          v-model="envVar.key"
                          placeholder="变量名"
                          class="env-key"
                        />
                        <VCInput 
                          v-model="envVar.value"
                          placeholder="变量值"
                          class="env-value"
                        />
                        <button 
                          @click="removeEnvVar(platform.key, index)"
                          class="btn btn-sm btn-danger"
                        >
                          <XMarkIcon class="w-3 h-3" />
                        </button>
                      </div>
                      <button 
                        @click="addEnvVar(platform.key)"
                        class="btn btn-sm btn-secondary"
                      >
                        <PlusIcon class="w-4 h-4 mr-1" />
                        添加环境变量
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 通用配置 -->
      <div class="general-config">
        <h4 class="section-title">通用配置</h4>
        <div class="config-form">
          <div class="form-group">
            <label class="form-label">默认环境</label>
            <VCSelect 
              v-model="generalConfig.defaultEnvironment"
              :options="environmentOptions"
              placeholder="选择默认环境"
            />
          </div>
          
          <div class="form-group">
            <label class="form-label">部署超时时间（分钟）</label>
            <VCInput 
              v-model="generalConfig.timeout"
              type="number"
              placeholder="30"
            />
          </div>
          
          <div class="form-group">
            <label class="form-label">并发部署数量</label>
            <VCInput 
              v-model="generalConfig.concurrency"
              type="number"
              placeholder="1"
            />
          </div>
          
          <div class="form-group">
            <label class="form-label">通知配置</label>
            <div class="notification-options">
              <label class="option-item">
                <input 
                  type="checkbox" 
                  v-model="generalConfig.notifications.email"
                  class="option-checkbox"
                />
                <span class="option-label">邮件通知</span>
              </label>
              
              <label class="option-item">
                <input 
                  type="checkbox" 
                  v-model="generalConfig.notifications.webhook"
                  class="option-checkbox"
                />
                <span class="option-label">Webhook 通知</span>
              </label>
            </div>
          </div>
        </div>
      </div>

      <!-- 配置预览 -->
      <div class="config-preview">
        <h4 class="section-title">配置预览</h4>
        <div class="preview-content">
          <pre class="config-json">{{ JSON.stringify(getAllConfigs(), null, 2) }}</pre>
        </div>
      </div>
    </div>

    <!-- 自定义按钮 -->
    <template #footer>
      <div class="modal-footer">
        <button 
          @click="handleCancel" 
          class="btn btn-secondary"
        >
          取消
        </button>
        <button 
          @click="resetConfigs" 
          class="btn btn-warning"
        >
          重置配置
        </button>
        <button 
          @click="handleConfirm" 
          :disabled="isSaving"
          :class="['btn', 'btn-primary', { 'loading': isSaving }]"
        >
          <span v-if="isSaving">保存中...</span>
          <span v-else>保存配置</span>
        </button>
      </div>
    </template>
  </VCModal>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import {
  XMarkIcon,
  PlusIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  DeviceTabletIcon
} from '@heroicons/vue/24/outline';

import { apiClient } from '../../../shared/api/VersionCraftAPIClient';
import { ErrorHandler } from '../../../shared/services/ErrorHandler';
import { NotificationService } from '../../../shared/services/NotificationService';

// 导入基础组件
import VCModal from '../ui/VCModal.vue';
import VCInput from '../ui/VCInput.vue';
import VCTextarea from '../ui/VCTextarea.vue';
import VCSelect from '../ui/VCSelect.vue';

// Props
interface Props {
  modelValue: boolean;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  save: [];
}>();

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const isSaving = ref(false);
const activeTab = ref('web-mobile');

const platforms = ref([
  {
    key: 'web-mobile',
    name: 'Web 移动端',
    icon: ComputerDesktopIcon
  },
  {
    key: 'android',
    name: 'Android',
    icon: DevicePhoneMobileIcon
  },
  {
    key: 'ios',
    name: 'iOS',
    icon: DeviceTabletIcon
  }
]);

const configs = ref<Record<string, any>>({
  'web-mobile': {
    buildCommand: 'npm run build',
    outputDir: 'dist',
    deployScript: '',
    envVars: []
  },
  'android': {
    buildCommand: 'npm run build:android',
    outputDir: 'platforms/android/app/build/outputs/apk',
    deployScript: '',
    envVars: []
  },
  'ios': {
    buildCommand: 'npm run build:ios',
    outputDir: 'platforms/ios/build',
    deployScript: '',
    envVars: []
  }
});

const generalConfig = ref({
  defaultEnvironment: 'staging',
  timeout: 30,
  concurrency: 1,
  notifications: {
    email: false,
    webhook: false
  }
});

// 计算属性
const environmentOptions = computed(() => [
  { label: '测试环境', value: 'staging' },
  { label: '生产环境', value: 'production' }
]);

// 生命周期
onMounted(async () => {
  await loadConfigs();
});

// 方法
const loadConfigs = async () => {
  try {
    const result = await apiClient.deploy.getConfig();
    
    if (result.success && result.data) {
      if (result.data.platforms) {
        configs.value = { ...configs.value, ...result.data.platforms };
      }
      if (result.data.general) {
        generalConfig.value = { ...generalConfig.value, ...result.data.general };
      }
    }
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Load Deploy Config');
  }
};

const addEnvVar = (platform: string) => {
  configs.value[platform].envVars.push({ key: '', value: '' });
};

const removeEnvVar = (platform: string, index: number) => {
  configs.value[platform].envVars.splice(index, 1);
};

const getAllConfigs = () => {
  return {
    platforms: configs.value,
    general: generalConfig.value
  };
};

const resetConfigs = () => {
  const confirmed = confirm('确定要重置所有配置吗？这将清除所有自定义设置。');
  if (confirmed) {
    configs.value = {
      'web-mobile': {
        buildCommand: 'npm run build',
        outputDir: 'dist',
        deployScript: '',
        envVars: []
      },
      'android': {
        buildCommand: 'npm run build:android',
        outputDir: 'platforms/android/app/build/outputs/apk',
        deployScript: '',
        envVars: []
      },
      'ios': {
        buildCommand: 'npm run build:ios',
        outputDir: 'platforms/ios/build',
        deployScript: '',
        envVars: []
      }
    };
    
    generalConfig.value = {
      defaultEnvironment: 'staging',
      timeout: 30,
      concurrency: 1,
      notifications: {
        email: false,
        webhook: false
      }
    };
    
    NotificationService.success('配置已重置');
  }
};

const handleConfirm = async () => {
  try {
    isSaving.value = true;
    
    const result = await apiClient.deploy.saveConfig(getAllConfigs());
    
    if (result.success) {
      NotificationService.success('部署配置保存成功');
      emit('save');
      handleCancel();
    } else {
      throw new Error(result.error || '保存配置失败');
    }
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Save Deploy Config');
  } finally {
    isSaving.value = false;
  }
};

const handleCancel = () => {
  visible.value = false;
  activeTab.value = 'web-mobile';
};
</script>

<style scoped>
/* 部署配置样式 */
.deploy-config {
  @apply space-y-6;
}

.section-title {
  @apply text-lg font-semibold text-gray-900 mb-4;
}

/* 配置标签页 */
.config-tabs {
  @apply space-y-4;
}

.tabs-nav {
  @apply flex space-x-2 border-b border-gray-200;
}

.tab-button {
  @apply inline-flex items-center px-4 py-2 text-sm font-medium rounded-t-lg transition-colors;
}

.tab-button:not(.tab-active) {
  @apply text-gray-600 hover:text-gray-800 hover:bg-gray-100;
}

.tab-active {
  @apply text-blue-600 bg-blue-50 border-b-2 border-blue-600;
}

.tab-content {
  @apply pt-4;
}

/* 表单样式 */
.config-form {
  @apply space-y-4;
}

.form-group {
  @apply space-y-2;
}

.form-label {
  @apply block text-sm font-medium text-gray-700;
}

/* 环境变量 */
.env-vars {
  @apply space-y-3;
}

.env-var-item {
  @apply flex items-center space-x-2;
}

.env-key {
  @apply flex-1;
}

.env-value {
  @apply flex-1;
}

/* 通知选项 */
.notification-options {
  @apply space-y-2;
}

.option-item {
  @apply flex items-center space-x-2 cursor-pointer;
}

.option-checkbox {
  @apply h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded;
}

.option-label {
  @apply text-sm text-gray-700;
}

/* 配置预览 */
.config-preview {
  @apply space-y-3;
}

.preview-content {
  @apply bg-gray-50 rounded-lg p-4 max-h-64 overflow-y-auto;
}

.config-json {
  @apply text-sm text-gray-700 font-mono whitespace-pre-wrap;
}

/* 按钮样式 */
.btn {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
}

.btn-warning {
  @apply bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500;
}

.btn-danger {
  @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}

.btn-sm {
  @apply px-3 py-1.5 text-xs;
}

.btn.loading {
  @apply opacity-75 cursor-not-allowed;
}

/* 模态框底部 */
.modal-footer {
  @apply flex items-center justify-end space-x-3;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tabs-nav {
    @apply flex-wrap;
  }

  .env-var-item {
    @apply flex-col space-y-2 space-x-0;
  }

  .env-key,
  .env-value {
    @apply w-full;
  }
}
</style>
