<!--
  创建标签模态框
  用于创建 Git 标签
-->

<template>
  <VCModal 
    v-model="visible" 
    title="创建 Git 标签" 
    :loading="isCreating"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="create-tag">
      <!-- 当前版本信息 -->
      <div class="current-version-info">
        <h4 class="section-title">当前版本信息</h4>
        <div class="version-card">
          <div class="version-main">
            <span class="version-number">{{ currentVersion }}</span>
            <span class="version-label">当前版本</span>
          </div>
          <div class="version-details">
            <div class="detail-item">
              <span class="detail-label">提交哈希:</span>
              <span class="detail-value">{{ currentCommit }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">分支:</span>
              <span class="detail-value">{{ currentBranch }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 标签信息 -->
      <div class="tag-info">
        <h4 class="section-title">标签信息</h4>
        <div class="form-grid">
          <div class="form-group">
            <label class="form-label">标签名称 *</label>
            <VCInput 
              v-model="tagName"
              placeholder="例如: v1.0.0"
              :error="tagNameError"
              @input="validateTagName"
            />
            <p class="form-help">建议使用语义化版本格式，如 v1.0.0</p>
          </div>
          
          <div class="form-group">
            <label class="form-label">标签类型</label>
            <div class="radio-group">
              <label class="radio-item">
                <input 
                  type="radio" 
                  v-model="tagType" 
                  value="lightweight"
                  class="radio-input"
                />
                <span class="radio-label">轻量标签</span>
                <span class="radio-description">简单的标签，只包含名称</span>
              </label>
              <label class="radio-item">
                <input 
                  type="radio" 
                  v-model="tagType" 
                  value="annotated"
                  class="radio-input"
                />
                <span class="radio-label">注释标签</span>
                <span class="radio-description">包含完整信息的标签（推荐）</span>
              </label>
            </div>
          </div>
        </div>
        
        <div v-if="tagType === 'annotated'" class="form-group">
          <label class="form-label">标签描述</label>
          <VCTextarea 
            v-model="tagMessage"
            placeholder="请输入标签描述..."
            :rows="4"
          />
          <p class="form-help">描述此标签的用途或重要性</p>
        </div>
      </div>

      <!-- 高级选项 -->
      <div class="advanced-options">
        <h4 class="section-title">高级选项</h4>
        <div class="options-list">
          <label class="option-item">
            <input 
              type="checkbox" 
              v-model="options.pushToRemote"
              class="option-checkbox"
            />
            <div class="option-content">
              <span class="option-label">推送到远程仓库</span>
              <span class="option-description">创建后自动推送标签到远程仓库</span>
            </div>
          </label>
          
          <label class="option-item">
            <input 
              type="checkbox" 
              v-model="options.createRelease"
              class="option-checkbox"
            />
            <div class="option-content">
              <span class="option-label">同时创建发布</span>
              <span class="option-description">基于此标签创建一个发布版本</span>
            </div>
          </label>
          
          <label class="option-item">
            <input 
              type="checkbox" 
              v-model="options.signTag"
              class="option-checkbox"
            />
            <div class="option-content">
              <span class="option-label">GPG 签名标签</span>
              <span class="option-description">使用 GPG 密钥对标签进行签名</span>
            </div>
          </label>
        </div>
      </div>

      <!-- 预览 -->
      <div class="tag-preview">
        <h4 class="section-title">标签预览</h4>
        <div class="preview-card">
          <div class="preview-header">
            <TagIcon class="w-5 h-5 text-blue-500" />
            <span class="preview-name">{{ tagName || '标签名称' }}</span>
            <span class="preview-type">{{ tagType === 'annotated' ? '注释标签' : '轻量标签' }}</span>
          </div>
          <div class="preview-content">
            <div class="preview-item">
              <span class="preview-label">目标版本:</span>
              <span class="preview-value">{{ currentVersion }}</span>
            </div>
            <div class="preview-item">
              <span class="preview-label">提交:</span>
              <span class="preview-value">{{ currentCommit }}</span>
            </div>
            <div v-if="tagMessage && tagType === 'annotated'" class="preview-item">
              <span class="preview-label">描述:</span>
              <span class="preview-value">{{ tagMessage }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 注意事项 -->
      <div class="tag-notes">
        <div class="notes-header">
          <InformationCircleIcon class="w-5 h-5 text-blue-500" />
          <span class="notes-title">注意事项</span>
        </div>
        <ul class="notes-list">
          <li>标签名称一旦创建不可修改，请仔细确认</li>
          <li>建议使用语义化版本命名，便于版本管理</li>
          <li>注释标签包含更多信息，推荐用于正式发布</li>
          <li>如果推送到远程仓库，请确保有相应权限</li>
        </ul>
      </div>
    </div>

    <!-- 自定义按钮 -->
    <template #footer>
      <div class="modal-footer">
        <button 
          @click="handleCancel" 
          class="btn btn-secondary"
        >
          取消
        </button>
        <button 
          @click="handleConfirm" 
          :disabled="!canCreate || isCreating"
          :class="['btn', 'btn-primary', { 'loading': isCreating }]"
        >
          <span v-if="isCreating">创建中...</span>
          <span v-else>创建标签</span>
        </button>
      </div>
    </template>
  </VCModal>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import {
  TagIcon,
  InformationCircleIcon
} from '@heroicons/vue/24/outline';

import { apiClient } from '../../../shared/api/VersionCraftAPIClient';
import { ErrorHandler } from '../../../shared/services/ErrorHandler';
import { NotificationService } from '../../../shared/services/NotificationService';

// 导入基础组件
import VCModal from '../ui/VCModal.vue';
import VCInput from '../ui/VCInput.vue';
import VCTextarea from '../ui/VCTextarea.vue';

// Props
interface Props {
  modelValue: boolean;
  currentVersion?: string;
}

const props = withDefaults(defineProps<Props>(), {
  currentVersion: ''
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  success: [tag: any];
}>();

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const isCreating = ref(false);
const tagName = ref('');
const tagMessage = ref('');
const tagType = ref<'lightweight' | 'annotated'>('annotated');
const tagNameError = ref('');
const currentCommit = ref('');
const currentBranch = ref('');

const options = ref({
  pushToRemote: false,
  createRelease: false,
  signTag: false
});

// 计算属性
const canCreate = computed(() => {
  return tagName.value && !tagNameError.value;
});

// 生命周期
onMounted(async () => {
  await loadGitInfo();
  
  // 自动生成标签名称
  if (props.currentVersion) {
    tagName.value = props.currentVersion.startsWith('v') ? props.currentVersion : `v${props.currentVersion}`;
    tagMessage.value = `Release ${props.currentVersion}`;
  }
});

// 方法
const loadGitInfo = async () => {
  try {
    // 使用现有的 API 方法获取当前版本信息
    const result = await apiClient.version.getCurrent();
    if (result.success && result.data) {
      // 从版本信息中提取 Git 相关信息
      currentCommit.value = result.data.commit?.substring(0, 8) || 'HEAD';
      currentBranch.value = result.data.branch || 'main';
    }
  } catch (error) {
    // 设置默认值
    currentCommit.value = 'HEAD';
    currentBranch.value = 'main';
  }
};

const validateTagName = () => {
  tagNameError.value = '';
  
  if (!tagName.value) {
    return;
  }
  
  // 检查标签名称格式
  const tagPattern = /^[a-zA-Z0-9\-_.]+$/;
  if (!tagPattern.test(tagName.value)) {
    tagNameError.value = '标签名称只能包含字母、数字、连字符、下划线和点';
    return;
  }
  
  // 检查长度
  if (tagName.value.length > 100) {
    tagNameError.value = '标签名称不能超过100个字符';
    return;
  }
  
  // 检查是否以点开头或结尾
  if (tagName.value.startsWith('.') || tagName.value.endsWith('.')) {
    tagNameError.value = '标签名称不能以点开头或结尾';
    return;
  }
};

const handleConfirm = async () => {
  if (!canCreate.value) return;
  
  try {
    isCreating.value = true;
    
    const result = await apiClient.version.createTag({
      name: tagName.value,
      message: tagType.value === 'annotated' ? tagMessage.value : undefined,
      type: tagType.value,
      push: options.value.pushToRemote,
      sign: options.value.signTag
    });
    
    if (result.success) {
      NotificationService.success(`标签 ${tagName.value} 创建成功`);
      
      // 如果选择同时创建发布
      if (options.value.createRelease) {
        // 这里可以触发创建发布的流程
        NotificationService.info('正在创建发布版本...');
      }
      
      emit('success', result.data);
      handleCancel();
    } else {
      throw new Error(result.error || '创建标签失败');
    }
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Create Tag');
  } finally {
    isCreating.value = false;
  }
};

const handleCancel = () => {
  visible.value = false;
  // 重置表单
  tagName.value = '';
  tagMessage.value = '';
  tagType.value = 'annotated';
  tagNameError.value = '';
  options.value = {
    pushToRemote: false,
    createRelease: false,
    signTag: false
  };
};
</script>

<style scoped>
/* 创建标签样式 */
.create-tag {
  @apply space-y-6;
}

.section-title {
  @apply text-lg font-semibold text-gray-900 mb-4;
}

/* 当前版本信息 */
.current-version-info {
  @apply space-y-3;
}

.version-card {
  @apply bg-blue-50 border border-blue-200 rounded-lg p-4;
}

.version-main {
  @apply flex items-center space-x-3 mb-3;
}

.version-number {
  @apply text-xl font-bold text-blue-600;
}

.version-label {
  @apply text-sm text-blue-700 bg-blue-100 px-2 py-1 rounded;
}

.version-details {
  @apply space-y-2;
}

.detail-item {
  @apply flex items-center justify-between text-sm;
}

.detail-label {
  @apply text-blue-700 font-medium;
}

.detail-value {
  @apply text-blue-900 font-mono;
}

/* 表单样式 */
.tag-info {
  @apply space-y-4;
}

.form-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4;
}

.form-group {
  @apply space-y-2;
}

.form-label {
  @apply block text-sm font-medium text-gray-700;
}

.form-help {
  @apply text-xs text-gray-500;
}

/* 单选按钮组 */
.radio-group {
  @apply space-y-3;
}

.radio-item {
  @apply flex items-start space-x-3 cursor-pointer p-3 border border-gray-200 rounded-lg hover:bg-gray-50;
}

.radio-input {
  @apply mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300;
}

.radio-label {
  @apply text-sm font-medium text-gray-900;
}

.radio-description {
  @apply block text-xs text-gray-500 mt-1;
}

/* 高级选项 */
.advanced-options {
  @apply space-y-4;
}

.options-list {
  @apply space-y-3;
}

.option-item {
  @apply flex items-start space-x-3 cursor-pointer p-3 border border-gray-200 rounded-lg hover:bg-gray-50;
}

.option-checkbox {
  @apply mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded;
}

.option-content {
  @apply flex-1;
}

.option-label {
  @apply text-sm font-medium text-gray-900;
}

.option-description {
  @apply block text-xs text-gray-500 mt-1;
}

/* 预览卡片 */
.tag-preview {
  @apply space-y-3;
}

.preview-card {
  @apply bg-green-50 border border-green-200 rounded-lg p-4;
}

.preview-header {
  @apply flex items-center space-x-2 mb-3;
}

.preview-name {
  @apply font-medium text-green-900;
}

.preview-type {
  @apply text-xs text-green-700 bg-green-100 px-2 py-1 rounded;
}

.preview-content {
  @apply space-y-2;
}

.preview-item {
  @apply flex items-center justify-between text-sm;
}

.preview-label {
  @apply text-green-700 font-medium;
}

.preview-value {
  @apply text-green-900 font-mono;
}

/* 注意事项 */
.tag-notes {
  @apply bg-blue-50 border border-blue-200 rounded-lg p-4;
}

.notes-header {
  @apply flex items-center space-x-2 mb-3;
}

.notes-title {
  @apply text-sm font-medium text-blue-800;
}

.notes-list {
  @apply space-y-2 text-sm text-blue-700;
}

.notes-list li {
  @apply flex items-start;
}

.notes-list li::before {
  content: "•";
  @apply text-blue-600 font-bold mr-2 mt-0.5 flex-shrink-0;
}

/* 模态框底部 */
.modal-footer {
  @apply flex items-center justify-end space-x-3;
}

.btn {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
}

.btn.loading {
  @apply opacity-75 cursor-not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-grid {
    @apply grid-cols-1;
  }

  .version-main {
    @apply flex-col items-start space-y-2 space-x-0;
  }

  .detail-item {
    @apply flex-col items-start;
  }

  .preview-item {
    @apply flex-col items-start;
  }
}
</style>
