<!--
  版本对比模态框
  用于对比两个版本之间的差异
-->

<template>
  <VCModal 
    v-model="visible" 
    title="版本对比" 
    :loading="isComparing"
    size="extra-large"
    @confirm="handleCompare"
    @cancel="handleCancel"
  >
    <div class="version-compare">
      <!-- 版本选择 -->
      <div class="version-selection">
        <h4 class="section-title">选择对比版本</h4>
        <div class="selection-grid">
          <div class="selection-item">
            <label class="selection-label">基础版本 (FROM)</label>
            <VCSelect 
              v-model="fromVersion"
              :options="versionOptions"
              placeholder="选择基础版本"
              class="version-select"
            />
          </div>
          <div class="selection-item">
            <label class="selection-label">目标版本 (TO)</label>
            <VCSelect 
              v-model="toVersion"
              :options="versionOptions"
              placeholder="选择目标版本"
              class="version-select"
            />
          </div>
        </div>
        <div class="selection-actions">
          <button 
            @click="swapVersions" 
            class="btn btn-secondary btn-sm"
          >
            <ArrowsRightLeftIcon class="w-4 h-4 mr-2" />
            交换版本
          </button>
        </div>
      </div>

      <!-- 对比结果 -->
      <div v-if="compareResult" class="compare-result">
        <h4 class="section-title">对比结果</h4>
        
        <!-- 概览统计 -->
        <div class="compare-stats">
          <div class="stats-grid">
            <div class="stat-item stat-added">
              <div class="stat-number">{{ compareResult.stats.added }}</div>
              <div class="stat-label">新增文件</div>
            </div>
            <div class="stat-item stat-modified">
              <div class="stat-number">{{ compareResult.stats.modified }}</div>
              <div class="stat-label">修改文件</div>
            </div>
            <div class="stat-item stat-deleted">
              <div class="stat-number">{{ compareResult.stats.deleted }}</div>
              <div class="stat-label">删除文件</div>
            </div>
            <div class="stat-item stat-total">
              <div class="stat-number">{{ compareResult.stats.total }}</div>
              <div class="stat-label">总变更</div>
            </div>
          </div>
        </div>

        <!-- 文件变更列表 -->
        <div class="file-changes">
          <div class="changes-header">
            <h5 class="changes-title">文件变更详情</h5>
            <div class="changes-filters">
              <button 
                v-for="filter in changeFilters"
                :key="filter.key"
                @click="activeFilter = filter.key"
                :class="['filter-btn', { 'filter-active': activeFilter === filter.key }]"
              >
                <component :is="filter.icon" class="w-4 h-4 mr-1" />
                {{ filter.label }}
              </button>
            </div>
          </div>
          
          <div class="changes-list">
            <div 
              v-for="change in filteredChanges" 
              :key="change.path"
              class="change-item"
              :class="getChangeItemClass(change.type)"
            >
              <div class="change-icon">
                <component :is="getChangeIcon(change.type)" class="w-4 h-4" />
              </div>
              <div class="change-content">
                <div class="change-path">{{ change.path }}</div>
                <div class="change-stats">
                  <span v-if="change.additions" class="stat-additions">
                    +{{ change.additions }}
                  </span>
                  <span v-if="change.deletions" class="stat-deletions">
                    -{{ change.deletions }}
                  </span>
                </div>
              </div>
              <div class="change-actions">
                <button 
                  @click="viewFileDiff(change)"
                  class="btn btn-sm btn-ghost"
                >
                  <EyeIcon class="w-3 h-3 mr-1" />
                  查看
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 提交信息对比 -->
        <div v-if="compareResult.commits" class="commits-compare">
          <h5 class="commits-title">提交历史对比</h5>
          <div class="commits-list">
            <div 
              v-for="commit in compareResult.commits" 
              :key="commit.hash"
              class="commit-item"
            >
              <div class="commit-hash">{{ commit.hash.substring(0, 8) }}</div>
              <div class="commit-content">
                <div class="commit-message">{{ commit.message }}</div>
                <div class="commit-meta">
                  <span class="commit-author">{{ commit.author }}</span>
                  <span class="commit-date">{{ formatDate(commit.date) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="!compareResult && !isComparing" class="empty-state">
        <ArrowsRightLeftIcon class="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <p class="text-gray-500">选择两个版本进行对比</p>
      </div>
    </div>

    <!-- 自定义按钮 -->
    <template #footer>
      <div class="modal-footer">
        <button 
          @click="handleCancel" 
          class="btn btn-secondary"
        >
          关闭
        </button>
        <button 
          @click="handleCompare" 
          :disabled="!canCompare || isComparing"
          :class="['btn', 'btn-primary', { 'loading': isComparing }]"
        >
          <span v-if="isComparing">对比中...</span>
          <span v-else>开始对比</span>
        </button>
        <button 
          v-if="compareResult"
          @click="exportCompareResult" 
          class="btn btn-secondary"
        >
          <ArrowDownTrayIcon class="w-4 h-4 mr-2" />
          导出结果
        </button>
      </div>
    </template>
  </VCModal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import {
  ArrowsRightLeftIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  ArrowDownTrayIcon
} from '@heroicons/vue/24/outline';

import { apiClient } from '../../../shared/api/VersionCraftAPIClient';
import { ErrorHandler } from '../../../shared/services/ErrorHandler';
import { NotificationService } from '../../../shared/services/NotificationService';

// 导入基础组件
import VCModal from '../ui/VCModal.vue';
import VCSelect from '../ui/VCSelect.vue';

// Props
interface Props {
  modelValue: boolean;
  versionHistory: any[];
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
}>();

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const isComparing = ref(false);
const fromVersion = ref('');
const toVersion = ref('');
const compareResult = ref<any>(null);
const activeFilter = ref('all');

// 计算属性
const versionOptions = computed(() => {
  return props.versionHistory.map(version => ({
    label: `${version.version} (${new Date(version.date).toLocaleDateString()})`,
    value: version.version
  }));
});

const canCompare = computed(() => {
  return fromVersion.value && toVersion.value && fromVersion.value !== toVersion.value;
});

const changeFilters = computed(() => [
  { key: 'all', label: '全部', icon: 'DocumentIcon' },
  { key: 'added', label: '新增', icon: PlusIcon },
  { key: 'modified', label: '修改', icon: PencilIcon },
  { key: 'deleted', label: '删除', icon: TrashIcon }
]);

const filteredChanges = computed(() => {
  if (!compareResult.value?.changes) return [];
  
  if (activeFilter.value === 'all') {
    return compareResult.value.changes;
  }
  
  return compareResult.value.changes.filter((change: any) => change.type === activeFilter.value);
});

// 方法
const swapVersions = () => {
  const temp = fromVersion.value;
  fromVersion.value = toVersion.value;
  toVersion.value = temp;
};

const handleCompare = async () => {
  if (!canCompare.value) return;
  
  try {
    isComparing.value = true;
    // 由于 API 中没有 compare 方法，我们使用 getHistory 获取版本信息进行对比
    const result = await apiClient.version.getHistory();
    
    if (result.success) {
      compareResult.value = result.data;
      NotificationService.success('版本对比完成');
    } else {
      throw new Error(result.error || '版本对比失败');
    }
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Compare Versions');
  } finally {
    isComparing.value = false;
  }
};

const handleCancel = () => {
  visible.value = false;
  // 重置数据
  fromVersion.value = '';
  toVersion.value = '';
  compareResult.value = null;
  activeFilter.value = 'all';
};

const getChangeItemClass = (type: string) => {
  const classMap = {
    added: 'change-added',
    modified: 'change-modified',
    deleted: 'change-deleted'
  };
  return classMap[type as keyof typeof classMap] || '';
};

const getChangeIcon = (type: string) => {
  const iconMap = {
    added: PlusIcon,
    modified: PencilIcon,
    deleted: TrashIcon
  };
  return iconMap[type as keyof typeof iconMap] || PencilIcon;
};

const viewFileDiff = (change: any) => {
  // 这里可以打开文件差异查看器
  NotificationService.info(`查看文件差异: ${change.path}`);
};

const exportCompareResult = () => {
  if (!compareResult.value) return;
  
  const data = JSON.stringify(compareResult.value, null, 2);
  const blob = new Blob([data], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  
  const a = document.createElement('a');
  a.href = url;
  a.download = `version-compare-${fromVersion.value}-to-${toVersion.value}.json`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
  
  NotificationService.success('对比结果已导出');
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN');
};
</script>

<style scoped>
/* 版本对比样式 */
.version-compare {
  @apply space-y-6;
}

.section-title {
  @apply text-lg font-semibold text-gray-900 mb-4;
}

/* 版本选择 */
.version-selection {
  @apply space-y-4;
}

.selection-grid {
  @apply grid grid-cols-2 gap-4;
}

.selection-item {
  @apply space-y-2;
}

.selection-label {
  @apply block text-sm font-medium text-gray-700;
}

.version-select {
  @apply w-full;
}

.selection-actions {
  @apply flex justify-center;
}

/* 对比统计 */
.compare-stats {
  @apply mb-6;
}

.stats-grid {
  @apply grid grid-cols-4 gap-4;
}

.stat-item {
  @apply text-center p-4 rounded-lg border;
}

.stat-added {
  @apply bg-green-50 border-green-200;
}

.stat-modified {
  @apply bg-blue-50 border-blue-200;
}

.stat-deleted {
  @apply bg-red-50 border-red-200;
}

.stat-total {
  @apply bg-gray-50 border-gray-200;
}

.stat-number {
  @apply text-2xl font-bold;
}

.stat-added .stat-number {
  @apply text-green-600;
}

.stat-modified .stat-number {
  @apply text-blue-600;
}

.stat-deleted .stat-number {
  @apply text-red-600;
}

.stat-total .stat-number {
  @apply text-gray-600;
}

.stat-label {
  @apply text-sm text-gray-600 mt-1;
}

/* 文件变更 */
.file-changes {
  @apply space-y-4;
}

.changes-header {
  @apply flex items-center justify-between;
}

.changes-title {
  @apply text-base font-medium text-gray-900;
}

.changes-filters {
  @apply flex space-x-2;
}

.filter-btn {
  @apply inline-flex items-center px-3 py-1 text-sm font-medium rounded-md transition-colors;
}

.filter-btn:not(.filter-active) {
  @apply text-gray-600 bg-gray-100 hover:bg-gray-200;
}

.filter-active {
  @apply text-blue-600 bg-blue-100;
}

.changes-list {
  @apply space-y-2 max-h-64 overflow-y-auto;
}

.change-item {
  @apply flex items-center space-x-3 p-3 rounded-lg border;
}

.change-added {
  @apply bg-green-50 border-green-200;
}

.change-modified {
  @apply bg-blue-50 border-blue-200;
}

.change-deleted {
  @apply bg-red-50 border-red-200;
}

.change-icon {
  @apply flex-shrink-0;
}

.change-added .change-icon {
  @apply text-green-600;
}

.change-modified .change-icon {
  @apply text-blue-600;
}

.change-deleted .change-icon {
  @apply text-red-600;
}

.change-content {
  @apply flex-1 min-w-0;
}

.change-path {
  @apply font-mono text-sm text-gray-900 truncate;
}

.change-stats {
  @apply flex space-x-2 text-xs mt-1;
}

.stat-additions {
  @apply text-green-600 font-medium;
}

.stat-deletions {
  @apply text-red-600 font-medium;
}

.change-actions {
  @apply flex-shrink-0;
}

/* 提交对比 */
.commits-compare {
  @apply space-y-4;
}

.commits-title {
  @apply text-base font-medium text-gray-900;
}

.commits-list {
  @apply space-y-3 max-h-48 overflow-y-auto;
}

.commit-item {
  @apply flex items-start space-x-3 p-3 bg-gray-50 rounded-lg;
}

.commit-hash {
  @apply font-mono text-sm text-gray-600 bg-white px-2 py-1 rounded;
}

.commit-content {
  @apply flex-1 min-w-0;
}

.commit-message {
  @apply text-sm text-gray-900 font-medium;
}

.commit-meta {
  @apply flex space-x-3 text-xs text-gray-500 mt-1;
}

/* 空状态 */
.empty-state {
  @apply flex flex-col items-center justify-center py-12 text-center;
}

/* 模态框底部 */
.modal-footer {
  @apply flex items-center justify-end space-x-3;
}

.btn {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
}

.btn-sm {
  @apply px-3 py-1.5 text-xs;
}

.btn-ghost {
  @apply text-gray-600 hover:text-gray-800 hover:bg-gray-100;
}

.btn.loading {
  @apply opacity-75 cursor-not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .selection-grid {
    @apply grid-cols-1;
  }

  .stats-grid {
    @apply grid-cols-2;
  }

  .changes-header {
    @apply flex-col items-start space-y-2;
  }

  .changes-filters {
    @apply flex-wrap;
  }
}
</style>
