<!--
  验证回滚模态框
  用于验证指定版本的回滚可行性
-->

<template>
  <VCModal 
    v-model="visible" 
    title="验证回滚可行性" 
    :loading="isValidating"
    size="large"
    @confirm="handleValidate"
    @cancel="handleCancel"
  >
    <div class="validate-form">
      <!-- 版本选择 -->
      <div class="version-selection">
        <h4 class="section-title">选择要验证的版本</h4>
        <div class="version-input-group">
          <VCSelect 
            v-model="selectedVersion"
            :options="versionOptions"
            placeholder="选择版本"
            :loading="isLoadingVersions"
            class="version-select"
          />
          <button 
            @click="loadVersions"
            :disabled="isLoadingVersions"
            class="btn btn-secondary refresh-btn"
          >
            <ArrowPathIcon class="w-4 h-4" />
          </button>
        </div>
        <p class="input-help">
          选择要验证回滚可行性的目标版本
        </p>
      </div>

      <!-- 验证选项 -->
      <div class="validation-options">
        <h4 class="section-title">验证选项</h4>
        <div class="options-list">
          <label class="option-item">
            <input 
              type="checkbox" 
              v-model="options.checkBuild" 
              class="option-checkbox"
            />
            <span class="option-label">检查构建产物</span>
            <span class="option-description">验证目标版本的构建产物是否存在且完整</span>
          </label>
          
          <label class="option-item">
            <input 
              type="checkbox" 
              v-model="options.checkDependencies" 
              class="option-checkbox"
            />
            <span class="option-label">检查依赖兼容性</span>
            <span class="option-description">验证目标版本的依赖是否与当前环境兼容</span>
          </label>
          
          <label class="option-item">
            <input 
              type="checkbox" 
              v-model="options.checkDatabase" 
              class="option-checkbox"
            />
            <span class="option-label">检查数据库兼容性</span>
            <span class="option-description">验证数据库结构是否与目标版本兼容</span>
          </label>
          
          <label class="option-item">
            <input 
              type="checkbox" 
              v-model="options.checkConfig" 
              class="option-checkbox"
            />
            <span class="option-label">检查配置文件</span>
            <span class="option-description">验证配置文件是否与目标版本兼容</span>
          </label>
        </div>
      </div>

      <!-- 验证结果 -->
      <div v-if="validationResult" class="validation-result">
        <h4 class="section-title">验证结果</h4>
        
        <!-- 总体结果 -->
        <div class="overall-result">
          <div :class="['result-header', validationResult.valid ? 'result-success' : 'result-error']">
            <component 
              :is="validationResult.valid ? CheckCircleIcon : XCircleIcon" 
              class="w-6 h-6"
            />
            <span class="result-text">
              {{ validationResult.valid ? '验证通过，可以安全回滚' : '验证失败，存在风险' }}
            </span>
          </div>
        </div>

        <!-- 详细结果 -->
        <div class="detailed-results">
          <!-- 成功项 -->
          <div v-if="validationResult.passed?.length" class="result-section">
            <h5 class="result-section-title success-title">
              <CheckCircleIcon class="w-4 h-4" />
              通过项 ({{ validationResult.passed.length }})
            </h5>
            <ul class="result-list success-list">
              <li v-for="item in validationResult.passed" :key="item" class="result-item">
                <CheckIcon class="w-4 h-4 text-green-500 flex-shrink-0 mt-0.5" />
                <span>{{ item }}</span>
              </li>
            </ul>
          </div>

          <!-- 失败项 -->
          <div v-if="validationResult.issues?.length" class="result-section">
            <h5 class="result-section-title error-title">
              <XCircleIcon class="w-4 h-4" />
              问题项 ({{ validationResult.issues.length }})
            </h5>
            <ul class="result-list error-list">
              <li v-for="issue in validationResult.issues" :key="issue" class="result-item">
                <XMarkIcon class="w-4 h-4 text-red-500 flex-shrink-0 mt-0.5" />
                <span>{{ issue }}</span>
              </li>
            </ul>
          </div>

          <!-- 警告项 -->
          <div v-if="validationResult.warnings?.length" class="result-section">
            <h5 class="result-section-title warning-title">
              <ExclamationTriangleIcon class="w-4 h-4" />
              警告项 ({{ validationResult.warnings.length }})
            </h5>
            <ul class="result-list warning-list">
              <li v-for="warning in validationResult.warnings" :key="warning" class="result-item">
                <ExclamationTriangleIcon class="w-4 h-4 text-yellow-500 flex-shrink-0 mt-0.5" />
                <span>{{ warning }}</span>
              </li>
            </ul>
          </div>
        </div>

        <!-- 建议操作 -->
        <div v-if="validationResult.suggestions?.length" class="suggestions">
          <h5 class="suggestions-title">建议操作:</h5>
          <ul class="suggestions-list">
            <li v-for="suggestion in validationResult.suggestions" :key="suggestion">
              {{ suggestion }}
            </li>
          </ul>
        </div>
      </div>

      <!-- 验证历史 -->
      <div v-if="validationHistory.length" class="validation-history">
        <h4 class="section-title">最近验证记录</h4>
        <div class="history-list">
          <div 
            v-for="record in validationHistory.slice(0, 3)" 
            :key="record.id"
            class="history-item"
          >
            <div class="history-header">
              <span class="history-version">{{ record.version }}</span>
              <span :class="['history-status', record.valid ? 'status-success' : 'status-error']">
                {{ record.valid ? '通过' : '失败' }}
              </span>
            </div>
            <div class="history-time">{{ formatTime(record.timestamp) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 自定义按钮 -->
    <template #footer>
      <div class="modal-footer">
        <button 
          @click="handleCancel" 
          class="btn btn-secondary"
        >
          关闭
        </button>
        <button 
          @click="handleValidate" 
          :disabled="!selectedVersion || isValidating"
          :class="['btn', 'btn-primary', { 'loading': isValidating }]"
        >
          <span v-if="isValidating">验证中...</span>
          <span v-else>开始验证</span>
        </button>
      </div>
    </template>
  </VCModal>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import {
  ArrowPathIcon,
  CheckCircleIcon,
  XCircleIcon,
  CheckIcon,
  XMarkIcon,
  ExclamationTriangleIcon
} from '@heroicons/vue/24/outline';

import { apiClient } from '../../../shared/api/VersionCraftAPIClient';
import { ErrorHandler } from '../../../shared/services/ErrorHandler';
import { NotificationService } from '../../../shared/services/NotificationService';

// 导入基础组件
import VCModal from '../ui/VCModal.vue';
import VCSelect from '../ui/VCSelect.vue';

// Props
interface Props {
  modelValue: boolean;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  validate: [version: string];
}>();

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const isValidating = ref(false);
const isLoadingVersions = ref(false);
const selectedVersion = ref('');
const versionOptions = ref<Array<{ label: string; value: string }>>([]);
const validationResult = ref<any>(null);
const validationHistory = ref<any[]>([]);

const options = ref({
  checkBuild: true,
  checkDependencies: true,
  checkDatabase: false,
  checkConfig: true
});

// 生命周期
onMounted(() => {
  loadVersions();
});

// 方法
const loadVersions = async () => {
  try {
    isLoadingVersions.value = true;
    const result = await apiClient.rollback.list();
    
    if (result.success && result.data) {
      versionOptions.value = result.data.map((version: any) => ({
        label: `${version.version} (${new Date(version.date).toLocaleDateString()})`,
        value: version.version
      }));
    }
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Load Versions');
  } finally {
    isLoadingVersions.value = false;
  }
};

const handleValidate = async () => {
  if (!selectedVersion.value) {
    NotificationService.warning('请选择要验证的版本');
    return;
  }
  
  try {
    isValidating.value = true;
    const result = await apiClient.rollback.validate(selectedVersion.value);
    
    if (result.success) {
      validationResult.value = result.data;
      
      // 添加到历史记录
      validationHistory.value.unshift({
        id: Date.now(),
        version: selectedVersion.value,
        valid: result.data.valid,
        timestamp: new Date().toISOString()
      });
      
      // 限制历史记录数量
      if (validationHistory.value.length > 10) {
        validationHistory.value = validationHistory.value.slice(0, 10);
      }
      
      emit('validate', selectedVersion.value);
      
      if (result.data.valid) {
        NotificationService.success('验证通过，可以安全回滚');
      } else {
        NotificationService.warning('验证失败，存在回滚风险');
      }
    } else {
      throw new Error(result.error || '验证失败');
    }
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Validate Rollback');
  } finally {
    isValidating.value = false;
  }
};

const handleCancel = () => {
  visible.value = false;
  // 重置表单
  selectedVersion.value = '';
  validationResult.value = null;
  options.value = {
    checkBuild: true,
    checkDependencies: true,
    checkDatabase: false,
    checkConfig: true
  };
};

const formatTime = (timestamp: string) => {
  const now = new Date();
  const time = new Date(timestamp);
  const diff = now.getTime() - time.getTime();
  
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);
  
  if (minutes < 1) return '刚刚';
  if (minutes < 60) return `${minutes}分钟前`;
  if (hours < 24) return `${hours}小时前`;
  if (days < 7) return `${days}天前`;
  
  return time.toLocaleString('zh-CN');
};
</script>

<style scoped>
/* 验证表单样式 */
.validate-form {
  @apply space-y-6;
}

.section-title {
  @apply text-lg font-semibold text-gray-900 mb-4;
}

/* 版本选择 */
.version-selection {
  @apply space-y-3;
}

.version-input-group {
  @apply flex items-center space-x-2;
}

.version-select {
  @apply flex-1;
}

.refresh-btn {
  @apply px-3 py-2;
}

.input-help {
  @apply text-xs text-gray-500;
}

/* 验证选项 */
.validation-options {
  @apply border rounded-lg p-4;
}

.options-list {
  @apply space-y-3;
}

.option-item {
  @apply flex items-start space-x-3 cursor-pointer;
}

.option-checkbox {
  @apply mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded;
}

.option-label {
  @apply text-sm font-medium text-gray-900;
}

.option-description {
  @apply block text-xs text-gray-500 mt-1;
}

/* 验证结果 */
.validation-result {
  @apply border rounded-lg p-4 space-y-4;
}

.overall-result {
  @apply pb-4 border-b border-gray-200;
}

.result-header {
  @apply flex items-center space-x-3;
}

.result-success {
  @apply text-green-700;
}

.result-error {
  @apply text-red-700;
}

.result-text {
  @apply text-lg font-medium;
}

/* 详细结果 */
.detailed-results {
  @apply space-y-4;
}

.result-section {
  @apply space-y-2;
}

.result-section-title {
  @apply flex items-center space-x-2 text-sm font-medium;
}

.success-title {
  @apply text-green-700;
}

.error-title {
  @apply text-red-700;
}

.warning-title {
  @apply text-yellow-700;
}

.result-list {
  @apply space-y-2 ml-6;
}

.result-item {
  @apply flex items-start space-x-2 text-sm;
}

.success-list .result-item {
  @apply text-green-700;
}

.error-list .result-item {
  @apply text-red-700;
}

.warning-list .result-item {
  @apply text-yellow-700;
}

/* 建议操作 */
.suggestions {
  @apply bg-blue-50 border border-blue-200 rounded p-3;
}

.suggestions-title {
  @apply text-sm font-medium text-blue-800 mb-2;
}

.suggestions-list {
  @apply space-y-1 text-sm text-blue-700;
}

.suggestions-list li {
  @apply flex items-start;
}

.suggestions-list li::before {
  content: "•";
  @apply text-blue-600 font-bold mr-2 mt-0.5 flex-shrink-0;
}

/* 验证历史 */
.validation-history {
  @apply space-y-3;
}

.history-list {
  @apply space-y-2;
}

.history-item {
  @apply bg-gray-50 rounded p-3;
}

.history-header {
  @apply flex items-center justify-between mb-1;
}

.history-version {
  @apply font-mono text-sm font-medium text-gray-900;
}

.history-status {
  @apply text-xs font-medium px-2 py-1 rounded-full;
}

.status-success {
  @apply bg-green-100 text-green-800;
}

.status-error {
  @apply bg-red-100 text-red-800;
}

.history-time {
  @apply text-xs text-gray-500;
}

/* 模态框底部 */
.modal-footer {
  @apply flex items-center justify-end space-x-3;
}

.btn {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn.loading {
  @apply opacity-75 cursor-not-allowed;
}
</style>
