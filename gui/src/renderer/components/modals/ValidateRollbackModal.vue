<!--
  验证回滚模态框
  用于验证指定版本的回滚可行性
-->

<template>
  <VCModal 
    v-model="visible" 
    title="验证回滚可行性" 
    :loading="isValidating"
    size="large"
    @confirm="handleValidate"
    @cancel="handleCancel"
  >
    <div class="validate-form">
      <!-- 版本选择 -->
      <div class="version-selection">
        <h4 class="section-title">选择要验证的版本</h4>
        <div class="version-input-group">
          <VCSelect 
            v-model="selectedVersion"
            :options="versionOptions"
            placeholder="选择版本"
            :loading="isLoadingVersions"
            class="version-select"
          />
          <button 
            @click="loadVersions"
            :disabled="isLoadingVersions"
            class="btn btn-secondary refresh-btn"
          >
            <ArrowPathIcon class="w-4 h-4" />
          </button>
        </div>
        <p class="input-help">
          选择要验证回滚可行性的目标版本
        </p>
      </div>

      <!-- 验证选项 -->
      <div class="validation-options">
        <h4 class="section-title">验证选项</h4>
        <div class="options-list">
          <label class="option-item">
            <input 
              type="checkbox" 
              v-model="options.checkBuild" 
              class="option-checkbox"
            />
            <span class="option-label">检查构建产物</span>
            <span class="option-description">验证目标版本的构建产物是否存在且完整</span>
          </label>
          
          <label class="option-item">
            <input 
              type="checkbox" 
              v-model="options.checkDependencies" 
              class="option-checkbox"
            />
            <span class="option-label">检查依赖兼容性</span>
            <span class="option-description">验证目标版本的依赖是否与当前环境兼容</span>
          </label>
          
          <label class="option-item">
            <input 
              type="checkbox" 
              v-model="options.checkDatabase" 
              class="option-checkbox"
            />
            <span class="option-label">检查数据库兼容性</span>
            <span class="option-description">验证数据库结构是否与目标版本兼容</span>
          </label>
          
          <label class="option-item">
            <input 
              type="checkbox" 
              v-model="options.checkConfig" 
              class="option-checkbox"
            />
            <span class="option-label">检查配置文件</span>
            <span class="option-description">验证配置文件是否与目标版本兼容</span>
          </label>
        </div>
      </div>

      <!-- 验证结果 -->
      <div v-if="validationResult" class="validation-result">
        <h4 class="section-title">验证结果</h4>
        
        <!-- 总体结果 -->
        <div class="overall-result">
          <div :class="['result-header', validationResult.valid ? 'result-success' : 'result-error']">
            <component 
              :is="validationResult.valid ? CheckCircleIcon : XCircleIcon" 
              class="w-6 h-6"
            />
            <span class="result-text">
              {{ validationResult.valid ? '验证通过，可以安全回滚' : '验证失败，存在风险' }}
            </span>
          </div>
        </div>

        <!-- 详细结果 -->
        <div class="detailed-results">
          <!-- 成功项 -->
          <div v-if="validationResult.passed?.length" class="result-section">
            <h5 class="result-section-title success-title">
              <CheckCircleIcon class="w-4 h-4" />
              通过项 ({{ validationResult.passed.length }})
            </h5>
            <ul class="result-list success-list">
              <li v-for="item in validationResult.passed" :key="item" class="result-item">
                <CheckIcon class="w-4 h-4 text-green-500 flex-shrink-0 mt-0.5" />
                <span>{{ item }}</span>
              </li>
            </ul>
          </div>

          <!-- 失败项 -->
          <div v-if="validationResult.issues?.length" class="result-section">
            <h5 class="result-section-title error-title">
              <XCircleIcon class="w-4 h-4" />
              问题项 ({{ validationResult.issues.length }})
            </h5>
            <ul class="result-list error-list">
              <li v-for="issue in validationResult.issues" :key="issue" class="result-item">
                <XMarkIcon class="w-4 h-4 text-red-500 flex-shrink-0 mt-0.5" />
                <span>{{ issue }}</span>
              </li>
            </ul>
          </div>

          <!-- 警告项 -->
          <div v-if="validationResult.warnings?.length" class="result-section">
            <h5 class="result-section-title warning-title">
              <ExclamationTriangleIcon class="w-4 h-4" />
              警告项 ({{ validationResult.warnings.length }})
            </h5>
            <ul class="result-list warning-list">
              <li v-for="warning in validationResult.warnings" :key="warning" class="result-item">
                <ExclamationTriangleIcon class="w-4 h-4 text-yellow-500 flex-shrink-0 mt-0.5" />
                <span>{{ warning }}</span>
              </li>
            </ul>
          </div>
        </div>

        <!-- 建议操作 -->
        <div v-if="validationResult.suggestions?.length" class="suggestions">
          <h5 class="suggestions-title">建议操作:</h5>
          <ul class="suggestions-list">
            <li v-for="suggestion in validationResult.suggestions" :key="suggestion">
              {{ suggestion }}
            </li>
          </ul>
        </div>
      </div>

      <!-- 验证历史 -->
      <div v-if="validationHistory.length" class="validation-history">
        <h4 class="section-title">最近验证记录</h4>
        <div class="history-list">
          <div 
            v-for="record in validationHistory.slice(0, 3)" 
            :key="record.id"
            class="history-item"
          >
            <div class="history-header">
              <span class="history-version">{{ record.version }}</span>
              <span :class="['history-status', record.valid ? 'status-success' : 'status-error']">
                {{ record.valid ? '通过' : '失败' }}
              </span>
            </div>
            <div class="history-time">{{ formatTime(record.timestamp) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 自定义按钮 -->
    <template #footer>
      <div class="modal-footer">
        <button 
          @click="handleCancel" 
          class="btn btn-secondary"
        >
          关闭
        </button>
        <button 
          @click="handleValidate" 
          :disabled="!selectedVersion || isValidating"
          :class="['btn', 'btn-primary', { 'loading': isValidating }]"
        >
          <span v-if="isValidating">验证中...</span>
          <span v-else>开始验证</span>
        </button>
      </div>
    </template>
  </VCModal>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import {
  ArrowPathIcon,
  CheckCircleIcon,
  XCircleIcon,
  CheckIcon,
  XMarkIcon,
  ExclamationTriangleIcon
} from '@heroicons/vue/24/outline';

import { apiClient } from '../../../shared/api/VersionCraftAPIClient';
import { ErrorHandler } from '../../../shared/services/ErrorHandler';
import { NotificationService } from '../../../shared/services/NotificationService';

// 导入基础组件
import VCModal from '../ui/VCModal.vue';
import VCSelect from '../ui/VCSelect.vue';

// Props
interface Props {
  modelValue: boolean;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  validate: [version: string];
}>();

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const isValidating = ref(false);
const isLoadingVersions = ref(false);
const selectedVersion = ref('');
const versionOptions = ref<Array<{ label: string; value: string }>>([]);
const validationResult = ref<any>(null);
const validationHistory = ref<any[]>([]);

const options = ref({
  checkBuild: true,
  checkDependencies: true,
  checkDatabase: false,
  checkConfig: true
});

// 生命周期
onMounted(() => {
  loadVersions();
});

// 方法
const loadVersions = async () => {
  try {
    isLoadingVersions.value = true;
    const result = await apiClient.rollback.list();
    
    if (result.success) {
      versionOptions.value = result.data.map((version: any) => ({
        label: `${version.version} (${new Date(version.date).toLocaleDateString()})`,
        value: version.version
      }));
    }
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Load Versions');
  } finally {
    isLoadingVersions.value = false;
  }
};

const handleValidate = async () => {
  if (!selectedVersion.value) {
    NotificationService.warning('请选择要验证的版本');
    return;
  }
  
  try {
    isValidating.value = true;
    const result = await apiClient.rollback.validate(selectedVersion.value);
    
    if (result.success) {
      validationResult.value = result.data;
      
      // 添加到历史记录
      validationHistory.value.unshift({
        id: Date.now(),
        version: selectedVersion.value,
        valid: result.data.valid,
        timestamp: new Date().toISOString()
      });
      
      // 限制历史记录数量
      if (validationHistory.value.length > 10) {
        validationHistory.value = validationHistory.value.slice(0, 10);
      }
      
      emit('validate', selectedVersion.value);
      
      if (result.data.valid) {
        NotificationService.success('验证通过，可以安全回滚');
      } else {
        NotificationService.warning('验证失败，存在回滚风险');
      }
    } else {
      throw new Error(result.error || '验证失败');
    }
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Validate Rollback');
  } finally {
    isValidating.value = false;
  }
};

const handleCancel = () => {
  visible.value = false;
  // 重置表单
  selectedVersion.value = '';
  validationResult.value = null;
  options.value = {
    checkBuild: true,
    checkDependencies: true,
    checkDatabase: false,
    checkConfig: true
  };
};

const formatTime = (timestamp: string) => {
  const now = new Date();
  const time = new Date(timestamp);
  const diff = now.getTime() - time.getTime();
  
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);
  
  if (minutes < 1) return '刚刚';
  if (minutes < 60) return `${minutes}分钟前`;
  if (hours < 24) return `${hours}小时前`;
  if (days < 7) return `${days}天前`;
  
  return time.toLocaleString('zh-CN');
};
</script>
