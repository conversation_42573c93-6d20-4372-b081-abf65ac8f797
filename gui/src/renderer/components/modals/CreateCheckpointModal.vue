<!--
  创建检查点模态框
  用于创建回滚检查点
-->

<template>
  <VCModal 
    v-model="visible" 
    title="创建回滚检查点" 
    :loading="isCreating"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="checkpoint-form">
      <!-- 检查点信息 -->
      <div class="checkpoint-info">
        <h4 class="section-title">检查点信息</h4>
        <div class="info-grid">
          <div class="info-item">
            <label class="info-label">当前版本:</label>
            <span class="version-tag">{{ currentVersion }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">创建时间:</label>
            <span class="time-text">{{ currentTime }}</span>
          </div>
        </div>
      </div>

      <!-- 检查点名称 -->
      <div class="checkpoint-name">
        <h4 class="section-title">检查点名称</h4>
        <VCInput 
          v-model="checkpointName"
          placeholder="输入检查点名称（可选）"
          :error="nameError"
          class="name-input"
        />
        <p class="input-help">
          如果不指定名称，将自动生成基于时间的名称
        </p>
      </div>

      <!-- 检查点描述 -->
      <div class="checkpoint-description">
        <h4 class="section-title">检查点描述</h4>
        <VCTextarea 
          v-model="checkpointDescription"
          placeholder="描述此检查点的用途或重要性（可选）"
          rows="3"
          class="description-textarea"
        />
      </div>

      <!-- 高级选项 -->
      <div class="advanced-options">
        <h4 class="section-title">高级选项</h4>
        <div class="options-list">
          <label class="option-item">
            <input 
              type="checkbox" 
              v-model="options.createTag" 
              class="option-checkbox"
            />
            <span class="option-label">创建 Git 标签</span>
            <span class="option-description">在 Git 仓库中创建对应的标签</span>
          </label>
          
          <label class="option-item">
            <input 
              type="checkbox" 
              v-model="options.pushToRemote" 
              class="option-checkbox"
            />
            <span class="option-label">推送到远程仓库</span>
            <span class="option-description">将标签推送到远程 Git 仓库</span>
          </label>
          
          <label class="option-item">
            <input 
              type="checkbox" 
              v-model="options.includeUncommitted" 
              class="option-checkbox"
            />
            <span class="option-label">包含未提交更改</span>
            <span class="option-description">将未提交的更改也包含在检查点中</span>
          </label>
        </div>
      </div>

      <!-- 预览信息 -->
      <div class="checkpoint-preview">
        <h4 class="section-title">检查点预览</h4>
        <div class="preview-card">
          <div class="preview-header">
            <BookmarkIcon class="w-5 h-5 text-blue-500" />
            <span class="preview-name">{{ finalCheckpointName }}</span>
          </div>
          <div class="preview-details">
            <div class="preview-item">
              <span class="preview-label">版本:</span>
              <span class="preview-value">{{ currentVersion }}</span>
            </div>
            <div class="preview-item">
              <span class="preview-label">标签:</span>
              <span class="preview-value">{{ finalTagName }}</span>
            </div>
            <div v-if="checkpointDescription" class="preview-item">
              <span class="preview-label">描述:</span>
              <span class="preview-value">{{ checkpointDescription }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 注意事项 -->
      <div class="checkpoint-notes">
        <div class="notes-header">
          <InformationCircleIcon class="w-5 h-5 text-blue-500" />
          <span class="notes-title">注意事项</span>
        </div>
        <ul class="notes-list">
          <li>检查点将保存当前版本的完整状态，便于后续回滚</li>
          <li>建议在重要操作前创建检查点，如版本发布、重大更新等</li>
          <li>检查点名称应该具有描述性，便于识别和管理</li>
          <li>如果选择推送到远程仓库，请确保有相应的权限</li>
        </ul>
      </div>
    </div>

    <!-- 自定义按钮 -->
    <template #footer>
      <div class="modal-footer">
        <button 
          @click="handleCancel" 
          class="btn btn-secondary"
        >
          取消
        </button>
        <button 
          @click="handleConfirm" 
          :disabled="isCreating"
          :class="['btn', 'btn-primary', { 'loading': isCreating }]"
        >
          <span v-if="isCreating">创建中...</span>
          <span v-else>创建检查点</span>
        </button>
      </div>
    </template>
  </VCModal>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import {
  BookmarkIcon,
  InformationCircleIcon
} from '@heroicons/vue/24/outline';

import { apiClient } from '../../../shared/api/VersionCraftAPIClient';
import { ErrorHandler } from '../../../shared/services/ErrorHandler';
import { NotificationService } from '../../../shared/services/NotificationService';

// 导入基础组件
import VCModal from '../ui/VCModal.vue';
import VCInput from '../ui/VCInput.vue';
import VCTextarea from '../ui/VCTextarea.vue';

// Props
interface Props {
  modelValue: boolean;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  success: [checkpoint: any];
}>();

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const isCreating = ref(false);
const currentVersion = ref('');
const currentTime = ref('');
const checkpointName = ref('');
const checkpointDescription = ref('');
const nameError = ref('');

const options = ref({
  createTag: true,
  pushToRemote: false,
  includeUncommitted: false
});

// 计算属性
const finalCheckpointName = computed(() => {
  if (checkpointName.value.trim()) {
    return checkpointName.value.trim();
  }
  return `checkpoint-${new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-')}`;
});

const finalTagName = computed(() => {
  return `${currentVersion.value}-${finalCheckpointName.value}`;
});

// 生命周期
onMounted(async () => {
  await loadCurrentVersion();
  updateCurrentTime();
  
  // 每秒更新时间
  setInterval(updateCurrentTime, 1000);
});

// 方法
const loadCurrentVersion = async () => {
  try {
    const result = await apiClient.version.getCurrent();
    if (result.success) {
      currentVersion.value = result.data?.version || '';
    }
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Load Current Version');
  }
};

const updateCurrentTime = () => {
  currentTime.value = new Date().toLocaleString('zh-CN');
};

const validateName = () => {
  nameError.value = '';
  
  if (checkpointName.value.trim()) {
    // 检查名称格式
    const namePattern = /^[a-zA-Z0-9\-_]+$/;
    if (!namePattern.test(checkpointName.value.trim())) {
      nameError.value = '检查点名称只能包含字母、数字、连字符和下划线';
      return false;
    }
    
    // 检查长度
    if (checkpointName.value.trim().length > 50) {
      nameError.value = '检查点名称不能超过50个字符';
      return false;
    }
  }
  
  return true;
};

const handleConfirm = async () => {
  if (!validateName()) {
    return;
  }
  
  try {
    isCreating.value = true;
    
    const result = await apiClient.rollback.createCheckpoint(
      checkpointName.value.trim() || undefined
    );
    
    if (result.success) {
      NotificationService.success('检查点创建成功');
      emit('success', result.data);
      handleCancel();
    } else {
      throw new Error(result.error || '创建检查点失败');
    }
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Create Checkpoint');
  } finally {
    isCreating.value = false;
  }
};

const handleCancel = () => {
  visible.value = false;
  // 重置表单
  checkpointName.value = '';
  checkpointDescription.value = '';
  nameError.value = '';
  options.value = {
    createTag: true,
    pushToRemote: false,
    includeUncommitted: false
  };
};
</script>

<style scoped>
/* 检查点表单样式 */
.checkpoint-form {
  @apply space-y-6;
}

.section-title {
  @apply text-lg font-semibold text-gray-900 mb-4;
}

/* 检查点信息 */
.checkpoint-info {
  @apply bg-gray-50 rounded-lg p-4;
}

.info-grid {
  @apply space-y-3;
}

.info-item {
  @apply flex items-center justify-between;
}

.info-label {
  @apply text-sm font-medium text-gray-600;
}

.version-tag {
  @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-mono font-medium bg-blue-100 text-blue-800;
}

.time-text {
  @apply text-sm text-gray-700;
}

/* 表单输入 */
.checkpoint-name,
.checkpoint-description {
  @apply space-y-3;
}

.name-input,
.description-textarea {
  @apply w-full;
}

.input-help {
  @apply text-xs text-gray-500;
}

/* 高级选项 */
.advanced-options {
  @apply border rounded-lg p-4;
}

.options-list {
  @apply space-y-3;
}

.option-item {
  @apply flex items-start space-x-3 cursor-pointer;
}

.option-checkbox {
  @apply mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded;
}

.option-label {
  @apply text-sm font-medium text-gray-900;
}

.option-description {
  @apply block text-xs text-gray-500 mt-1;
}

/* 预览卡片 */
.checkpoint-preview {
  @apply space-y-3;
}

.preview-card {
  @apply bg-blue-50 border border-blue-200 rounded-lg p-4;
}

.preview-header {
  @apply flex items-center space-x-2 mb-3;
}

.preview-name {
  @apply font-medium text-blue-900;
}

.preview-details {
  @apply space-y-2;
}

.preview-item {
  @apply flex items-center justify-between text-sm;
}

.preview-label {
  @apply text-blue-700 font-medium;
}

.preview-value {
  @apply text-blue-900 font-mono;
}

/* 注意事项 */
.checkpoint-notes {
  @apply bg-blue-50 border border-blue-200 rounded-lg p-4;
}

.notes-header {
  @apply flex items-center space-x-2 mb-3;
}

.notes-title {
  @apply text-sm font-medium text-blue-800;
}

.notes-list {
  @apply space-y-2 text-sm text-blue-700;
}

.notes-list li {
  @apply flex items-start;
}

.notes-list li::before {
  content: "•";
  @apply text-blue-600 font-bold mr-2 mt-0.5 flex-shrink-0;
}

/* 模态框底部 */
.modal-footer {
  @apply flex items-center justify-end space-x-3;
}

.btn {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn.loading {
  @apply opacity-75 cursor-not-allowed;
}
</style>
