<!--
  全局通知容器组件
  显示所有系统通知
-->

<template>
  <Teleport to="body">
    <!-- 通知容器 -->
    <div class="notification-container">
      <TransitionGroup name="notification" tag="div" class="notification-list">
        <div
          v-for="notification in notifications"
          :key="notification.id"
          :class="getNotificationClasses(notification)"
          class="notification-item"
        >
          <!-- 通知图标 -->
          <div class="notification-icon">
            <component :is="getNotificationIcon(notification.type)" class="w-5 h-5" />
          </div>

          <!-- 通知内容 -->
          <div class="notification-content">
            <div class="notification-header">
              <h4 class="notification-title">{{ notification.title }}</h4>
              <button
                @click="removeNotification(notification.id)"
                class="notification-close"
                aria-label="关闭通知"
              >
                <XMarkIcon class="w-4 h-4" />
              </button>
            </div>
            
            <p class="notification-message">{{ notification.message }}</p>
            
            <!-- 进度条 -->
            <div v-if="notification.progress !== undefined" class="notification-progress">
              <div class="progress-bar">
                <div 
                  class="progress-fill"
                  :style="{ width: `${notification.progress}%` }"
                ></div>
              </div>
              <span class="progress-text">{{ Math.round(notification.progress) }}%</span>
            </div>

            <!-- 操作按钮 -->
            <div v-if="notification.actions && notification.actions.length > 0" class="notification-actions">
              <button
                v-for="action in notification.actions"
                :key="action.label"
                @click="handleAction(action, notification.id)"
                :class="getActionClasses(action.variant)"
                class="action-button"
              >
                {{ action.label }}
              </button>
            </div>
          </div>

          <!-- 自动消失倒计时 -->
          <div 
            v-if="!notification.persistent && notification.duration > 0"
            class="notification-timer"
            :style="{ animationDuration: `${notification.duration}ms` }"
          ></div>
        </div>
      </TransitionGroup>
    </div>

    <!-- Toast 容器 -->
    <div class="toast-container">
      <TransitionGroup name="toast" tag="div" class="toast-list">
        <div
          v-for="toast in toasts"
          :key="toast.id"
          :class="getToastClasses(toast)"
          class="toast-item"
        >
          <div class="toast-content">
            <component :is="getNotificationIcon(toast.type)" class="w-4 h-4 toast-icon" />
            <span class="toast-message">{{ toast.message }}</span>
            <button
              @click="removeToast(toast.id)"
              class="toast-close"
              aria-label="关闭提示"
            >
              <XMarkIcon class="w-3 h-3" />
            </button>
          </div>
        </div>
      </TransitionGroup>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { 
  CheckCircleIcon, 
  ExclamationCircleIcon, 
  ExclamationTriangleIcon, 
  InformationCircleIcon,
  XMarkIcon 
} from '@heroicons/vue/24/outline';
import { notifications, toasts } from '@/shared/services/NotificationService';
import { NotificationService, ToastService } from '@/shared/services/NotificationService';
import type { NotificationAction } from '@/shared/services/ErrorHandler';

// 计算属性
const notificationList = computed(() => notifications.value);
const toastList = computed(() => toasts.value);

// 方法
const getNotificationClasses = (notification: any) => {
  const baseClasses = 'notification';
  const typeClasses = {
    success: 'notification-success',
    error: 'notification-error',
    warning: 'notification-warning',
    info: 'notification-info'
  };
  
  return [baseClasses, typeClasses[notification.type as keyof typeof typeClasses]];
};

const getToastClasses = (toast: any) => {
  const baseClasses = 'toast';
  const typeClasses = {
    success: 'toast-success',
    error: 'toast-error',
    warning: 'toast-warning',
    info: 'toast-info'
  };
  
  return [baseClasses, typeClasses[toast.type as keyof typeof typeClasses]];
};

const getNotificationIcon = (type: string) => {
  const icons = {
    success: CheckCircleIcon,
    error: ExclamationCircleIcon,
    warning: ExclamationTriangleIcon,
    info: InformationCircleIcon
  };
  
  return icons[type as keyof typeof icons] || InformationCircleIcon;
};

const getActionClasses = (variant?: string) => {
  const baseClasses = 'btn btn-sm';
  const variantClasses = {
    primary: 'btn-primary',
    secondary: 'btn-secondary'
  };
  
  return [baseClasses, variantClasses[variant as keyof typeof variantClasses] || 'btn-secondary'];
};

const removeNotification = (id: string) => {
  NotificationService.remove(id);
};

const removeToast = (id: string) => {
  ToastService.remove(id);
};

const handleAction = (action: NotificationAction, notificationId: string) => {
  try {
    action.action();
    // 执行操作后移除通知
    removeNotification(notificationId);
  } catch (error) {
    console.error('Error executing notification action:', error);
  }
};
</script>

<style scoped>
/* 通知容器 */
.notification-container {
  @apply fixed top-4 right-4 z-50 space-y-2;
  max-width: 400px;
}

.notification-list {
  @apply space-y-2;
}

.notification-item {
  @apply bg-white rounded-lg shadow-lg border p-4 relative overflow-hidden;
  min-width: 320px;
}

.notification {
  @apply border-l-4;
}

.notification-success {
  @apply border-l-green-500;
}

.notification-error {
  @apply border-l-red-500;
}

.notification-warning {
  @apply border-l-yellow-500;
}

.notification-info {
  @apply border-l-blue-500;
}

.notification-icon {
  @apply absolute top-4 left-4;
}

.notification-success .notification-icon {
  @apply text-green-500;
}

.notification-error .notification-icon {
  @apply text-red-500;
}

.notification-warning .notification-icon {
  @apply text-yellow-500;
}

.notification-info .notification-icon {
  @apply text-blue-500;
}

.notification-content {
  @apply ml-8;
}

.notification-header {
  @apply flex items-start justify-between mb-1;
}

.notification-title {
  @apply font-medium text-gray-900 text-sm;
}

.notification-close {
  @apply text-gray-400 hover:text-gray-600 transition-colors;
}

.notification-message {
  @apply text-gray-700 text-sm mb-3;
}

.notification-progress {
  @apply flex items-center space-x-2 mb-3;
}

.progress-bar {
  @apply flex-1 bg-gray-200 rounded-full h-2 overflow-hidden;
}

.progress-fill {
  @apply bg-blue-500 h-full transition-all duration-300 ease-out;
}

.progress-text {
  @apply text-xs text-gray-600 font-medium;
}

.notification-actions {
  @apply flex space-x-2;
}

.action-button {
  @apply px-3 py-1 text-xs font-medium rounded transition-colors;
}

.notification-timer {
  @apply absolute bottom-0 left-0 h-1 bg-gray-300;
  animation: timer-countdown linear;
}

@keyframes timer-countdown {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

/* Toast 容器 */
.toast-container {
  @apply fixed bottom-4 right-4 z-50 space-y-2;
}

.toast-list {
  @apply space-y-2;
}

.toast-item {
  @apply rounded-md shadow-md px-4 py-3;
  min-width: 280px;
}

.toast-success {
  @apply bg-green-500 text-white;
}

.toast-error {
  @apply bg-red-500 text-white;
}

.toast-warning {
  @apply bg-yellow-500 text-white;
}

.toast-info {
  @apply bg-blue-500 text-white;
}

.toast-content {
  @apply flex items-center space-x-2;
}

.toast-icon {
  @apply flex-shrink-0;
}

.toast-message {
  @apply flex-1 text-sm font-medium;
}

.toast-close {
  @apply text-white/80 hover:text-white transition-colors;
}

/* 按钮样式 */
.btn {
  @apply inline-flex items-center justify-center font-medium rounded transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-sm {
  @apply px-2 py-1 text-xs;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
}

/* 动画 */
.notification-enter-active,
.notification-leave-active {
  @apply transition-all duration-300 ease-out;
}

.notification-enter-from {
  @apply opacity-0 transform translate-x-full;
}

.notification-leave-to {
  @apply opacity-0 transform translate-x-full;
}

.toast-enter-active,
.toast-leave-active {
  @apply transition-all duration-200 ease-out;
}

.toast-enter-from {
  @apply opacity-0 transform translate-y-2;
}

.toast-leave-to {
  @apply opacity-0 transform translate-y-2;
}
</style>
