<template>
  <!-- {{ 优化通知组件尺寸和布局 }} -->
  <div class="fixed top-4 right-4 z-50 animate-fade-in">
    <div
      :class="notificationClasses"
      class="max-w-md w-full min-w-80 bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden"
      style="word-wrap: break-word; box-sizing: border-box;"
    >
      <div class="p-4">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <component :is="iconComponent" :class="iconClasses" class="h-6 w-6" />
          </div>
          <!-- {{ 优化消息文本布局，确保长文本正确显示 }} -->
          <div class="ml-3 flex-1 pt-0.5 min-w-0">
            <p class="text-sm font-medium text-gray-900 break-words leading-5">
              {{ message }}
            </p>
          </div>
          <div class="ml-4 flex-shrink-0 flex">
            <button
              @click="$emit('close')"
              class="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <span class="sr-only">关闭</span>
              <XMarkIcon class="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
      
      <!-- 进度条 -->
      <div v-if="showProgress" class="bg-gray-200 h-1">
        <div 
          :class="progressClasses"
          class="h-1 transition-all duration-100 ease-out"
          :style="{ width: `${progress}%` }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import {
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  InformationCircleIcon,
  XMarkIcon
} from '@heroicons/vue/24/outline';

interface Props {
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  duration?: number;
  showProgress?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  duration: 3000,
  showProgress: true
});

const emit = defineEmits<{
  close: [];
}>();

// 进度条
const progress = ref(100);

// 计算属性
const iconComponent = computed(() => {
  switch (props.type) {
    case 'success':
      return CheckCircleIcon;
    case 'error':
      return XCircleIcon;
    case 'warning':
      return ExclamationTriangleIcon;
    case 'info':
    default:
      return InformationCircleIcon;
  }
});

const iconClasses = computed(() => {
  switch (props.type) {
    case 'success':
      return 'text-green-400';
    case 'error':
      return 'text-red-400';
    case 'warning':
      return 'text-yellow-400';
    case 'info':
    default:
      return 'text-blue-400';
  }
});

const notificationClasses = computed(() => {
  switch (props.type) {
    case 'success':
      return 'border-l-4 border-green-400';
    case 'error':
      return 'border-l-4 border-red-400';
    case 'warning':
      return 'border-l-4 border-yellow-400';
    case 'info':
    default:
      return 'border-l-4 border-blue-400';
  }
});

const progressClasses = computed(() => {
  switch (props.type) {
    case 'success':
      return 'bg-green-400';
    case 'error':
      return 'bg-red-400';
    case 'warning':
      return 'bg-yellow-400';
    case 'info':
    default:
      return 'bg-blue-400';
  }
});

// 生命周期
onMounted(() => {
  if (props.duration > 0) {
    // 启动进度条动画
    const interval = setInterval(() => {
      progress.value -= 100 / (props.duration / 100);
      
      if (progress.value <= 0) {
        clearInterval(interval);
        emit('close');
      }
    }, 100);
  }
});
</script>

<style scoped>
/* 响应式样式，确保在不同屏幕尺寸下正常显示 */
@media (max-width: 640px) {
  .fixed.top-4.right-4 {
    left: 1rem;
    right: 1rem;
    top: 1rem;
  }

  .max-w-md {
    max-width: none;
  }

  .min-w-80 {
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .fixed.top-4.right-4 {
    left: 0.5rem;
    right: 0.5rem;
    top: 0.5rem;
  }

  .p-4 {
    padding: 0.75rem;
  }
}
</style>
