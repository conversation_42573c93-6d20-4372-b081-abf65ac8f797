import { createRouter, createWebHashHistory } from 'vue-router';
import type { RouteRecordRaw } from 'vue-router';

// 页面组件
import ProjectOverview from '../views/ProjectOverview.vue';
import VersionManagement from '../views/VersionManagement.vue';
import BuildManagement from '../views/BuildManagement.vue';
import DeployManagement from '../views/DeployManagement.vue';
import RollbackManagement from '../views/RollbackManagement.vue';
import ProjectConfig from '../views/ProjectConfig.vue';
import HotUpdateManagement from '../views/HotUpdateManagement.vue';

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'ProjectOverview',
    component: ProjectOverview,
    meta: {
      title: '项目概览'
    }
  },
  {
    path: '/version',
    name: 'VersionManagement',
    component: VersionManagement,
    meta: {
      title: '版本管理'
    }
  },
  {
    path: '/build',
    name: 'BuildManagement',
    component: BuildManagement,
    meta: {
      title: '构建管理'
    }
  },
  {
    path: '/deploy',
    name: 'DeployManagement',
    component: DeployManagement,
    meta: {
      title: '部署管理'
    }
  },
  {
    path: '/rollback',
    name: 'RollbackManagement',
    component: RollbackManagement,
    meta: {
      title: '回滚管理'
    }
  },
  {
    path: '/config',
    name: 'ProjectConfig',
    component: ProjectConfig,
    meta: {
      title: '项目配置'
    }
  },
  {
    path: '/hotupdate',
    name: 'HotUpdateManagement',
    component: HotUpdateManagement,
    meta: {
      title: '热更新管理'
    }
  }
];

const router = createRouter({
  history: createWebHashHistory(),
  routes
});

// 添加路由守卫来调试路由变化
router.beforeEach((to, from, next) => {
  console.log('🔍 [Router] Navigating from', from.path, 'to', to.path);
  next();
});

router.afterEach((to, from) => {
  console.log('🔍 [Router] Navigation completed to', to.path);
});

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - Version-Craft GUI`;
  }
  
  next();
});

export default router;
