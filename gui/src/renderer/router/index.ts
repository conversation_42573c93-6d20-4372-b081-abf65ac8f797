import { createRouter, createWebHashHistory } from 'vue-router';
import type { RouteRecordRaw } from 'vue-router';

// 页面组件
import ProjectOverview from '../views/ProjectOverview.vue';
import VersionManagement from '../views/VersionManagement.vue';
import BuildManagement from '../views/BuildManagement.vue';
import RollbackManagement from '../views/RollbackManagement.vue';
import ProjectConfig from '../views/ProjectConfig.vue';
import HotUpdateManagement from '../views/HotUpdateManagement.vue';

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'ProjectOverview',
    component: ProjectOverview,
    meta: {
      title: '项目概览'
    }
  },
  {
    path: '/version',
    name: 'VersionManagement',
    component: VersionManagement,
    meta: {
      title: '版本管理'
    }
  },
  {
    path: '/build',
    name: 'BuildManagement',
    component: BuildManagement,
    meta: {
      title: '构建管理'
    }
  },
  {
    path: '/rollback',
    name: 'RollbackManagement',
    component: RollbackManagement,
    meta: {
      title: '回滚管理'
    }
  },
  {
    path: '/config',
    name: 'ProjectConfig',
    component: ProjectConfig,
    meta: {
      title: '项目配置'
    }
  },
  {
    path: '/hotupdate',
    name: 'HotUpdateManagement',
    component: HotUpdateManagement,
    meta: {
      title: '热更新管理'
    }
  }
];

const router = createRouter({
  history: createWebHashHistory(),
  routes
});

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - Version-Craft GUI`;
  }
  
  next();
});

export default router;
