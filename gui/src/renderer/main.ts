import { createApp } from 'vue';
import { createPinia } from 'pinia';
import App from './App.vue';
import router from './router';

// 样式导入
import './assets/styles/main.css';

// 导入服务
import { ErrorHandler } from '@/shared/services/ErrorHandler';
import { NotificationService } from '@/shared/services/NotificationService';
import { Logger } from '@/shared/utils/Logger';

// 导入全局组件
import ErrorBoundary from '@/components/ErrorBoundary.vue';
import NotificationContainer from '@/components/NotificationContainer.vue';

// 创建应用实例
const app = createApp(App);

// 状态管理
const pinia = createPinia();
app.use(pinia);

// 路由
app.use(router);

// 注册全局组件
app.component('ErrorBoundary', ErrorBoundary);
app.component('NotificationContainer', NotificationContainer);

// 配置日志系统
Logger.configure({
  level: import.meta.env.DEV ? 0 : 1, // 开发环境显示所有日志，生产环境只显示 INFO 及以上
  enableConsole: true,
  enableFile: false,
  maxEntries: 1000
});

// 全局错误处理
app.config.errorHandler = (err, instance, info) => {
  // 使用统一的错误处理器
  ErrorHandler.handle(err as Error, 'Vue Global Error Handler', {
    componentInfo: info,
    instance: instance?.$options.name || 'Unknown'
  });
};

// 全局未捕获的 Promise 错误
window.addEventListener('unhandledrejection', (event) => {
  ErrorHandler.handle(
    new Error(event.reason?.message || 'Unhandled Promise Rejection'),
    'Unhandled Promise Rejection',
    {
      reason: event.reason,
      promise: event.promise
    }
  );
});

// 全局未捕获的错误
window.addEventListener('error', (event) => {
  ErrorHandler.handle(
    event.error || new Error(event.message),
    'Global Error Handler',
    {
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno
    }
  );
});

// 初始化通知系统
NotificationService.requestPermission().then(permission => {
  Logger.info('Notification permission:', permission);
});

// 挂载应用
app.mount('#app');

// 开发环境调试信息
if (import.meta.env.DEV) {
  Logger.info('🚀 Version-Craft GUI Started');
  Logger.info('🌍 Environment:', import.meta.env.MODE);
  Logger.info('📦 Vue Version:', app.version);

  // 开发环境下暴露服务到全局对象，方便调试
  (window as any).__VERSION_CRAFT_DEBUG__ = {
    ErrorHandler,
    NotificationService,
    Logger,
    app
  };
} else {
  Logger.info('Version-Craft GUI Started in Production Mode');
}
