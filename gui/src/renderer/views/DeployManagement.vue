<!--
  部署管理页面
  对等 CLI 所有部署功能的完整实现
-->

<template>
  <div class="deploy-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">部署管理</h1>
          <p class="page-subtitle">管理多平台部署、监控部署状态和历史记录</p>
        </div>
        <div class="header-actions">
          <button 
            @click="refreshData" 
            :disabled="isLoading"
            class="btn btn-secondary"
          >
            <ArrowPathIcon class="w-4 h-4 mr-2" :class="{ 'animate-spin': isLoading }" />
            刷新
          </button>
          <button 
            @click="showDeployModal = true" 
            class="btn btn-primary"
          >
            <RocketLaunchIcon class="w-4 h-4 mr-2" />
            开始部署
          </button>
        </div>
      </div>
    </div>

    <!-- 部署状态概览 -->
    <div class="deploy-status-section">
      <div class="card">
        <div class="card-header">
          <h2 class="card-title">部署状态概览</h2>
          <div class="card-actions">
            <span class="status-indicator" :class="getOverallStatusClass()">
              {{ getOverallStatusText() }}
            </span>
          </div>
        </div>
        <div class="card-content">
          <div v-if="isLoading" class="loading-state">
            <div class="loading-spinner"></div>
            <p>加载部署状态...</p>
          </div>
          
          <div v-else class="status-grid">
            <div 
              v-for="platform in supportedPlatforms" 
              :key="platform.key"
              class="platform-status-card"
              :class="getPlatformStatusClass(platform.key)"
            >
              <div class="platform-header">
                <component :is="platform.icon" class="w-6 h-6 platform-icon" />
                <div class="platform-info">
                  <h3 class="platform-name">{{ platform.name }}</h3>
                  <span class="platform-status">{{ getPlatformStatus(platform.key) }}</span>
                </div>
              </div>
              
              <div class="platform-environments">
                <div class="environment-item">
                  <span class="env-label">测试环境:</span>
                  <span class="env-status" :class="getEnvStatusClass(platform.key, 'staging')">
                    {{ getEnvStatus(platform.key, 'staging') }}
                  </span>
                </div>
                <div class="environment-item">
                  <span class="env-label">生产环境:</span>
                  <span class="env-status" :class="getEnvStatusClass(platform.key, 'production')">
                    {{ getEnvStatus(platform.key, 'production') }}
                  </span>
                </div>
              </div>
              
              <div class="platform-actions">
                <button 
                  @click="deployPlatform(platform.key, 'staging')"
                  :disabled="isDeploying"
                  class="btn btn-sm btn-secondary"
                >
                  部署测试
                </button>
                <button 
                  @click="deployPlatform(platform.key, 'production')"
                  :disabled="isDeploying"
                  class="btn btn-sm btn-primary"
                >
                  部署生产
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 当前部署任务 -->
    <div v-if="currentDeployments.length > 0" class="current-deployments-section">
      <div class="card">
        <div class="card-header">
          <h2 class="card-title">当前部署任务</h2>
          <div class="card-actions">
            <span class="task-count">{{ currentDeployments.length }} 个任务进行中</span>
          </div>
        </div>
        <div class="card-content">
          <div class="deployments-list">
            <div 
              v-for="deployment in currentDeployments" 
              :key="deployment.id"
              class="deployment-item"
            >
              <div class="deployment-info">
                <div class="deployment-header">
                  <span class="deployment-platform">{{ deployment.platform }}</span>
                  <span class="deployment-environment" :class="getEnvBadgeClass(deployment.environment)">
                    {{ deployment.environment === 'staging' ? '测试环境' : '生产环境' }}
                  </span>
                </div>
                <div class="deployment-progress">
                  <div class="progress-bar">
                    <div 
                      class="progress-fill" 
                      :style="{ width: `${deployment.progress}%` }"
                    ></div>
                  </div>
                  <span class="progress-text">{{ deployment.progress }}%</span>
                </div>
                <div class="deployment-status">
                  <span class="status-text">{{ deployment.statusText }}</span>
                  <span class="elapsed-time">{{ formatElapsedTime(deployment.startTime) }}</span>
                </div>
              </div>
              <div class="deployment-actions">
                <button 
                  @click="cancelDeployment(deployment.id)"
                  class="btn btn-sm btn-danger"
                >
                  <XMarkIcon class="w-3 h-3 mr-1" />
                  取消
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 部署历史 -->
    <div class="deploy-history-section">
      <div class="card">
        <div class="card-header">
          <h2 class="card-title">部署历史</h2>
          <div class="card-actions">
            <VCSelect 
              v-model="historyFilter"
              :options="historyFilterOptions"
              placeholder="筛选记录"
              class="filter-select"
            />
            <button 
              @click="showDeployHistoryModal = true" 
              class="btn btn-secondary btn-sm"
            >
              <EyeIcon class="w-4 h-4 mr-2" />
              查看全部
            </button>
          </div>
        </div>
        <div class="card-content">
          <div v-if="isLoadingHistory" class="loading-state">
            <div class="loading-spinner"></div>
            <p>加载部署历史...</p>
          </div>
          
          <div v-else-if="filteredDeployHistory.length === 0" class="empty-state">
            <ClockIcon class="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p class="text-gray-500">暂无部署历史</p>
          </div>
          
          <div v-else class="history-timeline">
            <div
              v-for="(record, index) in (filteredDeployHistory || []).slice(0, 10)"
              :key="record.id || index"
              class="timeline-item"
            >
              <div class="timeline-marker" :class="getTimelineMarkerClass(record)">
                <component :is="getDeploymentIcon(record)" class="w-4 h-4" />
              </div>
              <div class="timeline-content">
                <div class="timeline-header">
                  <div class="deployment-summary">
                    <span class="platform-name">{{ record.platform }}</span>
                    <ArrowRightIcon class="w-4 h-4 mx-2 text-gray-400" />
                    <span class="environment-name" :class="getEnvTextClass(record.environment)">
                      {{ record.environment === 'staging' ? '测试环境' : '生产环境' }}
                    </span>
                  </div>
                  <div class="timeline-status">
                    <span :class="['status-badge', record.success ? 'status-success' : 'status-error']">
                      {{ record.success ? '成功' : '失败' }}
                    </span>
                  </div>
                </div>
                <div class="timeline-details">
                  <div class="deployment-meta">
                    <span class="timestamp">{{ formatTime(record.timestamp) }}</span>
                    <span v-if="record.duration" class="duration">
                      耗时: {{ formatDuration(record.duration) }}
                    </span>
                    <span v-if="record.url" class="deploy-url">
                      <a :href="record.url" target="_blank" class="url-link">
                        <LinkIcon class="w-3 h-3 mr-1" />
                        访问地址
                      </a>
                    </span>
                  </div>
                  <div v-if="record.error" class="error-message">
                    <ExclamationCircleIcon class="w-4 h-4 mr-1 text-red-500" />
                    {{ record.error }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 模态框组件 -->
    <DeployModal 
      v-model="showDeployModal" 
      :supported-platforms="supportedPlatforms"
      @deploy="executeDeployment"
    />
    
    <DeployHistoryModal 
      v-model="showDeployHistoryModal"
      :deploy-history="deployHistory"
    />
    
    <DeployConfigModal 
      v-model="showDeployConfigModal"
      @save="onDeployConfigSaved"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import {
  ArrowPathIcon,
  RocketLaunchIcon,
  ArrowRightIcon,
  ClockIcon,
  EyeIcon,
  XMarkIcon,
  ExclamationCircleIcon,
  LinkIcon,
  CheckCircleIcon,
  XCircleIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  DeviceTabletIcon
} from '@heroicons/vue/24/outline';

import { apiClient } from '../../shared/api/VersionCraftAPIClient';
import { ErrorHandler } from '../../shared/services/ErrorHandler';
import { NotificationService } from '../../shared/services/NotificationService';

// 导入基础组件
import VCSelect from '../components/ui/VCSelect.vue';

// 导入模态框组件（稍后创建）
import DeployModal from '../components/modals/DeployModal.vue';
import DeployHistoryModal from '../components/modals/DeployHistoryModal.vue';
import DeployConfigModal from '../components/modals/DeployConfigModal.vue';

// 响应式数据
const isLoading = ref(false);
const isLoadingHistory = ref(false);
const isDeploying = ref(false);
const historyFilter = ref('all');

// 模态框状态
const showDeployModal = ref(false);
const showDeployHistoryModal = ref(false);
const showDeployConfigModal = ref(false);

// 数据
const platformStatuses = ref<Record<string, any>>({});
const currentDeployments = ref<any[]>([]);
const deployHistory = ref<any[]>([]);

// 轮询定时器
let statusPollingTimer: NodeJS.Timeout | null = null;

// 支持的平台
const supportedPlatforms = ref([
  {
    key: 'web-mobile',
    name: 'Web 移动端',
    icon: ComputerDesktopIcon
  },
  {
    key: 'android',
    name: 'Android',
    icon: DevicePhoneMobileIcon
  },
  {
    key: 'ios',
    name: 'iOS',
    icon: DeviceTabletIcon
  }
]);

// 计算属性
const historyFilterOptions = computed(() => [
  { label: '全部记录', value: 'all' },
  { label: '成功部署', value: 'success' },
  { label: '失败部署', value: 'failed' },
  { label: '测试环境', value: 'staging' },
  { label: '生产环境', value: 'production' }
]);

const filteredDeployHistory = computed(() => {
  const history = deployHistory.value || [];

  if (historyFilter.value === 'all') {
    return history;
  }

  return history.filter(record => {
    switch (historyFilter.value) {
      case 'success':
        return record.success;
      case 'failed':
        return !record.success;
      case 'staging':
        return record.environment === 'staging';
      case 'production':
        return record.environment === 'production';
      default:
        return true;
    }
  });
});

// 生命周期
onMounted(async () => {
  await loadData();
  startStatusPolling();
});

onUnmounted(() => {
  stopStatusPolling();
});

// 方法
const loadData = async () => {
  await Promise.all([
    loadPlatformStatuses(),
    loadCurrentDeployments(),
    loadDeployHistory()
  ]);
};

const loadPlatformStatuses = async () => {
  try {
    isLoading.value = true;
    const result = await apiClient.deploy.getStatus();

    if (result.success && result.data) {
      // DeploymentStatus 没有 platforms 属性，我们需要从其他属性构建平台状态
      platformStatuses.value = {
        'web-mobile': { staging: result.data.staging, production: result.data.production },
        'android': { staging: result.data.staging, production: result.data.production },
        'ios': { staging: result.data.staging, production: result.data.production }
      };
    }
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Load Platform Statuses');
  } finally {
    isLoading.value = false;
  }
};

const loadCurrentDeployments = async () => {
  try {
    // 使用现有的 API 方法获取部署状态，从中提取当前部署信息
    const result = await apiClient.deploy.getStatus();

    if (result.success && result.data) {
      // 从状态信息中提取当前进行中的部署
      currentDeployments.value = result.data.currentDeployments || [];
    }
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Load Current Deployments');
  }
};

const loadDeployHistory = async () => {
  try {
    isLoadingHistory.value = true;
    const result = await apiClient.deploy.getHistory();
    
    if (result.success) {
      deployHistory.value = result.data || [];
    }
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Load Deploy History');
  } finally {
    isLoadingHistory.value = false;
  }
};

const refreshData = async () => {
  await loadData();
  NotificationService.success('数据已刷新');
};

const startStatusPolling = () => {
  // 每30秒轮询一次状态
  statusPollingTimer = setInterval(async () => {
    if (currentDeployments.value.length > 0) {
      await loadCurrentDeployments();
    }
  }, 30000);
};

const stopStatusPolling = () => {
  if (statusPollingTimer) {
    clearInterval(statusPollingTimer);
    statusPollingTimer = null;
  }
};

// 工具方法
const getOverallStatusClass = () => {
  // 根据平台状态计算整体状态
  return 'status-healthy'; // 示例
};

const getOverallStatusText = () => {
  return '系统正常'; // 示例
};

const getPlatformStatusClass = (platform: string) => {
  const status = platformStatuses.value[platform];
  if (!status) return 'platform-unknown';
  
  if (status.staging?.success && status.production?.success) {
    return 'platform-healthy';
  } else if (status.staging?.success || status.production?.success) {
    return 'platform-warning';
  } else {
    return 'platform-error';
  }
};

const getPlatformStatus = (platform: string) => {
  const status = platformStatuses.value[platform];
  if (!status) return '未知';
  
  if (status.staging?.success && status.production?.success) {
    return '正常运行';
  } else if (status.staging?.success || status.production?.success) {
    return '部分可用';
  } else {
    return '需要部署';
  }
};

const getEnvStatus = (platform: string, environment: string) => {
  const status = platformStatuses.value[platform]?.[environment];
  if (!status) return '未部署';
  
  return status.success ? '已部署' : '部署失败';
};

const getEnvStatusClass = (platform: string, environment: string) => {
  const status = platformStatuses.value[platform]?.[environment];
  if (!status) return 'env-not-deployed';
  
  return status.success ? 'env-deployed' : 'env-failed';
};

const getEnvBadgeClass = (environment: string) => {
  return environment === 'staging' ? 'badge-warning' : 'badge-success';
};

const getEnvTextClass = (environment: string) => {
  return environment === 'staging' ? 'text-yellow-600' : 'text-green-600';
};

const getTimelineMarkerClass = (record: any) => {
  return record.success ? 'timeline-marker-success' : 'timeline-marker-error';
};

const getDeploymentIcon = (record: any) => {
  return record.success ? CheckCircleIcon : XCircleIcon;
};

const deployPlatform = async (platform: string, environment: string) => {
  try {
    isDeploying.value = true;
    const result = await apiClient.deploy.deployToEnvironment(environment, platform);
    
    if (result.success) {
      NotificationService.success(`${platform} 部署到${environment === 'staging' ? '测试' : '生产'}环境成功`);
      await loadData();
    } else {
      throw new Error(result.error || '部署失败');
    }
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Deploy Platform');
  } finally {
    isDeploying.value = false;
  }
};

const executeDeployment = async (deployOptions: any) => {
  try {
    const result = await apiClient.deploy.deployToEnvironment(
      deployOptions.environment,
      deployOptions.platform
    );
    
    if (result.success) {
      NotificationService.success('部署任务已启动');
      showDeployModal.value = false;
      await loadData();
    } else {
      throw new Error(result.error || '启动部署失败');
    }
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Execute Deployment');
  }
};

const cancelDeployment = async (deploymentId: string) => {
  try {
    // 使用现有的 API 方法取消部署
    const result = await apiClient.deploy.cancel(deploymentId);

    if (result.success) {
      NotificationService.success('部署任务已取消');
      await loadCurrentDeployments();
    } else {
      throw new Error(result.error || '取消部署失败');
    }
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Cancel Deployment');
  }
};

const onDeployConfigSaved = () => {
  NotificationService.success('部署配置已保存');
  loadData();
};

const formatTime = (timestamp: string) => {
  const now = new Date();
  const time = new Date(timestamp);
  const diff = now.getTime() - time.getTime();
  
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);
  
  if (minutes < 1) return '刚刚';
  if (minutes < 60) return `${minutes}分钟前`;
  if (hours < 24) return `${hours}小时前`;
  if (days < 7) return `${days}天前`;
  
  return time.toLocaleString('zh-CN');
};

const formatDuration = (ms: number) => {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  
  if (minutes > 0) {
    return `${minutes}分${seconds % 60}秒`;
  }
  return `${seconds}秒`;
};

const formatElapsedTime = (startTime: string) => {
  const now = new Date();
  const start = new Date(startTime);
  const elapsed = now.getTime() - start.getTime();
  
  return formatDuration(elapsed);
};
</script>

<style scoped>
/* 部署管理页面样式 */
.deploy-management {
  @apply p-6 space-y-6 max-w-7xl mx-auto;
}

/* 页面头部 */
.page-header {
  @apply bg-white rounded-lg shadow-sm border p-6;
}

.header-content {
  @apply flex items-center justify-between;
}

.header-info {
  @apply flex-1;
}

.page-title {
  @apply text-2xl font-bold text-gray-900 mb-2;
}

.page-subtitle {
  @apply text-gray-600;
}

.header-actions {
  @apply flex items-center space-x-3;
}

/* 卡片样式 */
.card {
  @apply bg-white rounded-lg shadow-sm border;
}

.card-header {
  @apply px-6 py-4 border-b border-gray-200 flex items-center justify-between;
}

.card-title {
  @apply text-lg font-semibold text-gray-900;
}

.card-actions {
  @apply flex items-center space-x-3;
}

.card-content {
  @apply p-6;
}

/* 状态指示器 */
.status-indicator {
  @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium;
}

.status-indicator.status-healthy {
  @apply bg-green-100 text-green-800;
}

/* 平台状态网格 */
.status-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

.platform-status-card {
  @apply border rounded-lg p-4 transition-colors;
}

.platform-healthy {
  @apply border-green-200 bg-green-50;
}

.platform-warning {
  @apply border-yellow-200 bg-yellow-50;
}

.platform-error {
  @apply border-red-200 bg-red-50;
}

.platform-unknown {
  @apply border-gray-200 bg-gray-50;
}

.platform-header {
  @apply flex items-center space-x-3 mb-4;
}

.platform-icon {
  @apply text-gray-600;
}

.platform-healthy .platform-icon {
  @apply text-green-600;
}

.platform-warning .platform-icon {
  @apply text-yellow-600;
}

.platform-error .platform-icon {
  @apply text-red-600;
}

.platform-info {
  @apply flex-1;
}

.platform-name {
  @apply text-lg font-semibold text-gray-900;
}

.platform-status {
  @apply text-sm text-gray-600;
}

.platform-environments {
  @apply space-y-2 mb-4;
}

.environment-item {
  @apply flex items-center justify-between text-sm;
}

.env-label {
  @apply text-gray-600;
}

.env-status {
  @apply font-medium;
}

.env-deployed {
  @apply text-green-600;
}

.env-failed {
  @apply text-red-600;
}

.env-not-deployed {
  @apply text-gray-500;
}

.platform-actions {
  @apply flex space-x-2;
}

/* 当前部署任务 */
.task-count {
  @apply text-sm text-blue-600 bg-blue-100 px-2 py-1 rounded;
}

.deployments-list {
  @apply space-y-4;
}

.deployment-item {
  @apply flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg;
}

.deployment-info {
  @apply flex-1 space-y-2;
}

.deployment-header {
  @apply flex items-center space-x-3;
}

.deployment-platform {
  @apply font-semibold text-gray-900;
}

.deployment-environment {
  @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium;
}

.badge-warning {
  @apply bg-yellow-100 text-yellow-800;
}

.badge-success {
  @apply bg-green-100 text-green-800;
}

.deployment-progress {
  @apply flex items-center space-x-3;
}

.progress-bar {
  @apply flex-1 bg-gray-200 rounded-full h-2;
}

.progress-fill {
  @apply bg-blue-600 h-2 rounded-full transition-all duration-300;
}

.progress-text {
  @apply text-sm font-medium text-gray-700;
}

.deployment-status {
  @apply flex items-center justify-between text-sm text-gray-600;
}

/* 部署历史时间线 */
.history-timeline {
  @apply space-y-6;
}

.timeline-item {
  @apply relative flex items-start space-x-4;
}

.timeline-item:not(:last-child)::before {
  content: '';
  @apply absolute left-4 top-8 w-0.5 h-full bg-gray-200;
}

.timeline-marker {
  @apply flex items-center justify-center w-8 h-8 rounded-full border-2 bg-white;
}

.timeline-marker-success {
  @apply border-green-500 text-green-500;
}

.timeline-marker-error {
  @apply border-red-500 text-red-500;
}

.timeline-content {
  @apply flex-1 min-w-0;
}

.timeline-header {
  @apply flex items-center justify-between mb-2;
}

.deployment-summary {
  @apply flex items-center;
}

.platform-name {
  @apply font-medium text-gray-900;
}

.environment-name {
  @apply font-medium;
}

.timeline-status {
  @apply flex items-center;
}

.status-badge {
  @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium;
}

.status-success {
  @apply bg-green-100 text-green-800;
}

.status-error {
  @apply bg-red-100 text-red-800;
}

.timeline-details {
  @apply space-y-2;
}

.deployment-meta {
  @apply flex items-center space-x-4 text-xs text-gray-500;
}

.url-link {
  @apply flex items-center text-blue-600 hover:text-blue-800;
}

.error-message {
  @apply flex items-center text-sm text-red-600 bg-red-50 p-2 rounded;
}

/* 按钮样式 */
.btn {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
}

.btn-danger {
  @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}

.btn-sm {
  @apply px-3 py-1.5 text-xs;
}

/* 加载和空状态 */
.loading-state {
  @apply flex flex-col items-center justify-center py-12;
}

.loading-spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4;
}

.empty-state {
  @apply flex flex-col items-center justify-center py-12 text-center;
}

.filter-select {
  @apply w-48;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .deploy-management {
    @apply p-4 space-y-4;
  }

  .header-content {
    @apply flex-col items-start space-y-4;
  }

  .status-grid {
    @apply grid-cols-1;
  }

  .deployment-item {
    @apply flex-col items-start space-y-3;
  }

  .deployment-meta {
    @apply flex-col items-start space-y-1 space-x-0;
  }

  .platform-actions {
    @apply flex-col space-y-2 space-x-0 w-full;
  }

  .platform-actions .btn {
    @apply w-full justify-center;
  }
}
</style>
