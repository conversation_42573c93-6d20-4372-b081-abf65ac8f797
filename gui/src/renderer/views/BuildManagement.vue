<!--
  构建管理页面
  对等 CLI 所有构建功能的完整实现
-->

<template>
  <div class="build-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">构建管理</h1>
          <p class="page-subtitle">管理多平台构建任务，监控构建状态和历史记录</p>
        </div>
        <div class="header-actions">
          <button
            @click="refreshData"
            :disabled="isLoading"
            class="btn btn-secondary"
          >
            <ArrowPathIcon class="w-4 h-4 mr-2" :class="{ 'animate-spin': isLoading }" />
            刷新
          </button>
          <button
            @click="showBuildConfigModal = true"
            class="btn btn-secondary"
          >
            <CogIcon class="w-4 h-4 mr-2" />
            构建配置
          </button>
          <button
            @click="showBuildModal = true"
            class="btn btn-primary"
          >
            <PlayIcon class="w-4 h-4 mr-2" />
            开始构建
          </button>
        </div>
      </div>
    </div>

    <!-- 当前构建状态 -->
    <div v-if="currentBuilds.length > 0" class="current-builds-section">
      <div class="card">
        <div class="card-header">
          <h2 class="card-title">当前构建任务</h2>
          <div class="card-actions">
            <span class="task-count">{{ currentBuilds.length }} 个任务进行中</span>
          </div>
        </div>
        <div class="card-content">
          <div class="builds-list">
            <div
              v-for="build in currentBuilds"
              :key="build.id"
              class="build-item"
            >
              <div class="build-info">
                <div class="build-header">
                  <div class="build-platform">
                    <component :is="getPlatformIcon(build.platform)" class="w-5 h-5 mr-2" />
                    <span class="platform-name">{{ getPlatformName(build.platform) }}</span>
                  </div>
                  <div class="build-type">
                    <span class="type-badge" :class="getBuildTypeClass(build.type)">
                      {{ getBuildTypeText(build.type) }}
                    </span>
                  </div>
                </div>
                <div class="build-progress">
                  <div class="progress-bar">
                    <div
                      class="progress-fill"
                      :style="{ width: `${build.progress}%` }"
                    ></div>
                  </div>
                  <span class="progress-text">{{ build.progress }}%</span>
                </div>
                <div class="build-status">
                  <span class="status-text">{{ build.statusText }}</span>
                  <span class="elapsed-time">{{ formatElapsedTime(build.startTime) }}</span>
                </div>
              </div>
              <div class="build-actions">
                <button
                  @click="showBuildLogs(build)"
                  class="btn btn-sm btn-ghost"
                >
                  <EyeIcon class="w-3 h-3 mr-1" />
                  日志
                </button>
                <button
                  @click="cancelBuild(build.id)"
                  class="btn btn-sm btn-danger"
                >
                  <XMarkIcon class="w-3 h-3 mr-1" />
                  取消
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速构建 -->
    <div class="quick-build-section">
      <div class="card">
        <div class="card-header">
          <h2 class="card-title">快速构建</h2>
          <div class="card-actions">
            <VCSelect
              v-model="quickBuildType"
              :options="buildTypeOptions"
              placeholder="构建类型"
              class="build-type-select"
            />
          </div>
        </div>
        <div class="card-content">
          <div class="platforms-grid">
            <div
              v-for="platform in platforms"
              :key="platform.id"
              class="platform-card"
              :class="{ 'platform-building': isPlatformBuilding(platform.id) }"
            >
              <div class="platform-header">
                <div class="platform-icon-wrapper">
                  <component :is="getPlatformIcon(platform.id)" class="w-6 h-6 platform-icon" />
                </div>
                <div class="platform-info">
                  <h3 class="platform-name">{{ platform.name }}</h3>
                  <p class="platform-description">{{ platform.description }}</p>
                </div>
                <div class="platform-status">
                  <span :class="['status-dot', getPlatformStatusClass(platform.id)]"></span>
                </div>
              </div>

              <div class="platform-stats">
                <div class="stat-item">
                  <span class="stat-label">上次构建:</span>
                  <span class="stat-value">{{ getLastBuildTime(platform.id) }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">构建次数:</span>
                  <span class="stat-value">{{ getBuildCount(platform.id) }}</span>
                </div>
              </div>

              <div class="platform-actions">
                <button
                  @click="quickBuild(platform.id)"
                  :disabled="isBuilding || isPlatformBuilding(platform.id)"
                  class="btn btn-primary btn-sm platform-build-btn"
                >
                  <PlayIcon class="w-3 h-3 mr-1" />
                  {{ isPlatformBuilding(platform.id) ? '构建中...' : '快速构建' }}
                </button>
                <button
                  @click="showPlatformConfig(platform.id)"
                  class="btn btn-ghost btn-sm"
                >
                  <CogIcon class="w-3 h-3 mr-1" />
                  配置
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 构建统计 -->
    <div class="build-stats-section">
      <div class="card">
        <div class="card-header">
          <h2 class="card-title">构建统计</h2>
          <div class="card-actions">
            <VCSelect
              v-model="statsTimeRange"
              :options="timeRangeOptions"
              placeholder="时间范围"
              class="time-range-select"
            />
          </div>
        </div>
        <div class="card-content">
          <div v-if="isLoadingStats" class="loading-state">
            <div class="loading-spinner"></div>
            <p>加载统计信息...</p>
          </div>

          <div v-else class="stats-grid">
            <div class="stat-card">
              <div class="stat-icon">
                <PlayIcon class="w-6 h-6 text-blue-500" />
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ buildStats.totalBuilds || 0 }}</div>
                <div class="stat-label">总构建次数</div>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon">
                <CheckCircleIcon class="w-6 h-6 text-green-500" />
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ buildStats.successfulBuilds || 0 }}</div>
                <div class="stat-label">成功构建</div>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon">
                <XCircleIcon class="w-6 h-6 text-red-500" />
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ buildStats.failedBuilds || 0 }}</div>
                <div class="stat-label">失败构建</div>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon">
                <ClockIcon class="w-6 h-6 text-purple-500" />
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ formatDuration(buildStats.averageDuration || 0) }}</div>
                <div class="stat-label">平均耗时</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 构建历史 -->
    <div class="build-history-section">
      <div class="card">
        <div class="card-header">
          <h2 class="card-title">构建历史</h2>
          <div class="card-actions">
            <VCSelect
              v-model="historyFilter"
              :options="historyFilterOptions"
              placeholder="筛选记录"
              class="filter-select"
            />
            <button
              @click="showBuildHistoryModal = true"
              class="btn btn-secondary btn-sm"
            >
              <EyeIcon class="w-4 h-4 mr-2" />
              查看全部
            </button>
          </div>
        </div>
        <div class="card-content">
          <div v-if="isLoadingHistory" class="loading-state">
            <div class="loading-spinner"></div>
            <p>加载构建历史...</p>
          </div>

          <div v-else-if="filteredBuildHistory.length === 0" class="empty-state">
            <ClockIcon class="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p class="text-gray-500">暂无构建历史</p>
          </div>

          <div v-else class="history-timeline">
            <div
              v-for="(build, index) in filteredBuildHistory.slice(0, 10)"
              :key="build.id || index"
              class="timeline-item"
            >
              <div class="timeline-marker" :class="getTimelineMarkerClass(build)">
                <component :is="getBuildIcon(build)" class="w-4 h-4" />
              </div>
              <div class="timeline-content">
                <div class="timeline-header">
                  <div class="build-summary">
                    <component :is="getPlatformIcon(build.platform)" class="w-4 h-4 mr-2" />
                    <span class="platform-name">{{ getPlatformName(build.platform) }}</span>
                    <ArrowRightIcon class="w-4 h-4 mx-2 text-gray-400" />
                    <span class="build-version">{{ build.version }}</span>
                  </div>
                  <div class="timeline-status">
                    <span :class="['status-badge', build.success ? 'status-success' : 'status-error']">
                      {{ build.success ? '成功' : '失败' }}
                    </span>
                  </div>
                </div>
                <div class="timeline-details">
                  <div class="build-meta">
                    <span class="timestamp">{{ formatTime(build.timestamp) }}</span>
                    <span v-if="build.duration" class="duration">
                      耗时: {{ formatDuration(build.duration) }}
                    </span>
                    <span v-if="build.size" class="build-size">
                      大小: {{ formatSize(build.size) }}
                    </span>
                  </div>
                  <div v-if="build.error" class="error-message">
                    <ExclamationCircleIcon class="w-4 h-4 mr-1 text-red-500" />
                    {{ build.error }}
                  </div>
                </div>
                <div class="timeline-actions">
                  <button
                    @click="showBuildDetails(build)"
                    class="btn btn-sm btn-ghost"
                  >
                    <EyeIcon class="w-3 h-3 mr-1" />
                    详情
                  </button>
                  <button
                    v-if="build.success && build.artifactPath"
                    @click="downloadArtifact(build)"
                    class="btn btn-sm btn-ghost"
                  >
                    <ArrowDownTrayIcon class="w-3 h-3 mr-1" />
                    下载
                  </button>
                  <button
                    v-if="build.success"
                    @click="rebuildVersion(build)"
                    class="btn btn-sm btn-ghost"
                  >
                    <ArrowPathIcon class="w-3 h-3 mr-1" />
                    重新构建
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 模态框组件（暂时注释掉） -->
    <!--
    <BuildModal
      v-model="showBuildModal"
      :platforms="platforms"
      @build="executeBuild"
    />

    <BuildConfigModal
      v-model="showBuildConfigModal"
      @save="onBuildConfigSaved"
    />

    <BuildHistoryModal
      v-model="showBuildHistoryModal"
      :build-history="buildHistory"
    />

    <BuildDetailsModal
      v-model="showBuildDetailsModal"
      :build-record="selectedBuild"
    />

    <BuildLogsModal
      v-model="showBuildLogsModal"
      :build-logs="selectedBuildLogs"
    />
    -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import {
  ArrowPathIcon,
  CogIcon,
  PlayIcon,
  EyeIcon,
  XMarkIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  ArrowRightIcon,
  ExclamationCircleIcon,
  ArrowDownTrayIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  DeviceTabletIcon
} from '@heroicons/vue/24/outline';

import { apiClient } from '../../shared/api/VersionCraftAPIClient';
import { ErrorHandler } from '../../shared/services/ErrorHandler';
import { NotificationService } from '../../shared/services/NotificationService';

// 导入基础组件
import VCSelect from '../components/ui/VCSelect.vue';

// 导入模态框组件（暂时注释掉，稍后创建）
// import BuildModal from '../components/modals/BuildModal.vue';
// import BuildConfigModal from '../components/modals/BuildConfigModal.vue';
// import BuildHistoryModal from '../components/modals/BuildHistoryModal.vue';
// import BuildDetailsModal from '../components/modals/BuildDetailsModal.vue';
// import BuildLogsModal from '../components/modals/BuildLogsModal.vue';

// 响应式数据
const isLoading = ref(false);
const isBuilding = ref(false);
const isLoadingStats = ref(false);
const isLoadingHistory = ref(false);
const quickBuildType = ref('debug');
const statsTimeRange = ref('week');
const historyFilter = ref('all');

// 模态框状态
const showBuildModal = ref(false);
const showBuildConfigModal = ref(false);
const showBuildHistoryModal = ref(false);
const showBuildDetailsModal = ref(false);
const showBuildLogsModal = ref(false);

// 数据
const currentBuilds = ref<any[]>([]);
const buildHistory = ref<any[]>([]);
const buildStats = ref<any>({});
const selectedBuild = ref<any>(null);
const selectedBuildLogs = ref<string[]>([]);

// 轮询定时器
let buildPollingTimer: NodeJS.Timeout | null = null;

// 平台配置
const platforms = ref([
  {
    id: 'web-mobile',
    name: 'Web 移动端',
    description: '移动端 Web 应用',
    icon: ComputerDesktopIcon
  },
  {
    id: 'android',
    name: 'Android',
    description: 'Android 应用',
    icon: DevicePhoneMobileIcon
  },
  {
    id: 'ios',
    name: 'iOS',
    description: 'iOS 应用',
    icon: DeviceTabletIcon
  }
]);

// 计算属性
const buildTypeOptions = computed(() => [
  { label: '调试构建', value: 'debug' },
  { label: '发布构建', value: 'release' },
  { label: '测试构建', value: 'test' }
]);

const timeRangeOptions = computed(() => [
  { label: '最近一周', value: 'week' },
  { label: '最近一月', value: 'month' },
  { label: '最近三月', value: 'quarter' }
]);

const historyFilterOptions = computed(() => [
  { label: '全部记录', value: 'all' },
  { label: '成功构建', value: 'success' },
  { label: '失败构建', value: 'failed' },
  { label: 'Web 移动端', value: 'web-mobile' },
  { label: 'Android', value: 'android' },
  { label: 'iOS', value: 'ios' }
]);

const filteredBuildHistory = computed(() => {
  const history = buildHistory.value || [];

  if (historyFilter.value === 'all') {
    return history;
  }

  return history.filter(build => {
    switch (historyFilter.value) {
      case 'success':
        return build.success;
      case 'failed':
        return !build.success;
      case 'web-mobile':
      case 'android':
      case 'ios':
        return build.platform === historyFilter.value;
      default:
        return true;
    }
  });
});

// 生命周期
onMounted(async () => {
  await loadData();
  startBuildPolling();
});

onUnmounted(() => {
  stopBuildPolling();
});

// 方法
const loadData = async () => {
  await Promise.all([
    loadCurrentBuilds(),
    loadBuildHistory(),
    loadBuildStats()
  ]);
};

const loadCurrentBuilds = async () => {
  try {
    // 从构建历史中筛选出正在进行的构建
    const result = await apiClient.build.getHistory();

    if (result.success && result.data) {
      // 筛选出状态为 'running' 的构建作为当前构建
      currentBuilds.value = result.data.filter((build: any) => build.status === 'running') || [];
    }
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Load Current Builds');
  }
};

const loadBuildHistory = async () => {
  try {
    isLoadingHistory.value = true;
    const result = await apiClient.build.getHistory();

    if (result.success) {
      buildHistory.value = result.data || [];
    }
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Load Build History');
  } finally {
    isLoadingHistory.value = false;
  }
};

const loadBuildStats = async () => {
  try {
    isLoadingStats.value = true;
    const result = await apiClient.build.getStats();

    if (result.success) {
      buildStats.value = result.data || {};
    }
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Load Build Stats');
  } finally {
    isLoadingStats.value = false;
  }
};

const refreshData = async () => {
  isLoading.value = true;
  await loadData();
  isLoading.value = false;
  NotificationService.success('数据已刷新');
};

const startBuildPolling = () => {
  // 每10秒轮询一次构建状态
  buildPollingTimer = setInterval(async () => {
    if (currentBuilds.value.length > 0) {
      await loadCurrentBuilds();
    }
  }, 10000);
};

const stopBuildPolling = () => {
  if (buildPollingTimer) {
    clearInterval(buildPollingTimer);
    buildPollingTimer = null;
  }
};

// 构建相关方法
const quickBuild = async (platformId: string) => {
  try {
    isBuilding.value = true;
    const result = await apiClient.build.start(platformId, {
      type: quickBuildType.value
    });

    if (result.success) {
      NotificationService.success(`${getPlatformName(platformId)} 构建已启动`);
      await loadCurrentBuilds();
    } else {
      throw new Error(result.error || '启动构建失败');
    }
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Quick Build');
  } finally {
    isBuilding.value = false;
  }
};

const executeBuild = async (buildOptions: any) => {
  try {
    const result = await apiClient.build.start(buildOptions.platform, buildOptions);

    if (result.success) {
      NotificationService.success('构建任务已启动');
      showBuildModal.value = false;
      await loadCurrentBuilds();
    } else {
      throw new Error(result.error || '启动构建失败');
    }
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Execute Build');
  }
};

const cancelBuild = async (buildId: string) => {
  try {
    const result = await apiClient.build.cancel(buildId);

    if (result.success) {
      NotificationService.success('构建任务已取消');
      await loadCurrentBuilds();
    } else {
      throw new Error(result.error || '取消构建失败');
    }
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Cancel Build');
  }
};

const showBuildLogs = (build: any) => {
  selectedBuildLogs.value = build.logs || [];
  showBuildLogsModal.value = true;
};

const showBuildDetails = (build: any) => {
  selectedBuild.value = build;
  showBuildDetailsModal.value = true;
};

const showPlatformConfig = (platformId: string) => {
  // 显示平台特定配置
  NotificationService.info(`配置 ${getPlatformName(platformId)} 平台`);
};

const downloadArtifact = (build: any) => {
  if (build.artifactPath) {
    // 触发下载
    NotificationService.success('开始下载构建产物');
  }
};

const rebuildVersion = async (build: any) => {
  const confirmed = confirm(`确定要重新构建 ${build.platform} ${build.version} 吗？`);
  if (!confirmed) return;

  await quickBuild(build.platform);
};

// 事件处理
const onBuildConfigSaved = () => {
  NotificationService.success('构建配置已保存');
  loadData();
};

// 工具方法
const isPlatformBuilding = (platformId: string) => {
  return currentBuilds.value.some(build => build.platform === platformId);
};

const getPlatformIcon = (platform: string) => {
  const iconMap = {
    'web-mobile': ComputerDesktopIcon,
    'android': DevicePhoneMobileIcon,
    'ios': DeviceTabletIcon
  };
  return iconMap[platform as keyof typeof iconMap] || ComputerDesktopIcon;
};

const getPlatformName = (platform: string) => {
  const nameMap = {
    'web-mobile': 'Web 移动端',
    'android': 'Android',
    'ios': 'iOS'
  };
  return nameMap[platform as keyof typeof nameMap] || platform;
};

const getPlatformStatusClass = (platformId: string) => {
  // 根据平台状态返回样式类
  return 'status-ready'; // 示例
};

const getLastBuildTime = (platformId: string) => {
  const lastBuild = buildHistory.value.find(build => build.platform === platformId);
  return lastBuild ? formatTime(lastBuild.timestamp) : '从未构建';
};

const getBuildCount = (platformId: string) => {
  return buildHistory.value.filter(build => build.platform === platformId).length;
};

const getBuildTypeClass = (type: string) => {
  const classMap = {
    debug: 'type-debug',
    release: 'type-release',
    test: 'type-test'
  };
  return classMap[type as keyof typeof classMap] || 'type-debug';
};

const getBuildTypeText = (type: string) => {
  const textMap = {
    debug: '调试版本',
    release: '发布版本',
    test: '测试版本'
  };
  return textMap[type as keyof typeof textMap] || type;
};

const getTimelineMarkerClass = (build: any) => {
  return build.success ? 'timeline-marker-success' : 'timeline-marker-error';
};

const getBuildIcon = (build: any) => {
  return build.success ? CheckCircleIcon : XCircleIcon;
};

const formatElapsedTime = (startTime: string) => {
  const now = new Date();
  const start = new Date(startTime);
  const elapsed = now.getTime() - start.getTime();

  return formatDuration(elapsed);
};

const formatTime = (timestamp: string) => {
  const now = new Date();
  const time = new Date(timestamp);
  const diff = now.getTime() - time.getTime();

  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);

  if (minutes < 1) return '刚刚';
  if (minutes < 60) return `${minutes}分钟前`;
  if (hours < 24) return `${hours}小时前`;
  if (days < 7) return `${days}天前`;

  return time.toLocaleString('zh-CN');
};

const formatDuration = (ms: number) => {
  if (!ms) return '-';

  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);

  if (minutes > 0) {
    return `${minutes}分${seconds % 60}秒`;
  }
  return `${seconds}秒`;
};

const formatSize = (bytes: number) => {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};





</script>

<style scoped>
/* 构建管理页面样式 */
.build-management {
  @apply p-6 space-y-6 max-w-7xl mx-auto;
}

/* 页面头部 */
.page-header {
  @apply bg-white rounded-lg shadow-sm border p-6;
}

.header-content {
  @apply flex items-center justify-between;
}

.header-info {
  @apply flex-1;
}

.page-title {
  @apply text-2xl font-bold text-gray-900 mb-2;
}

.page-subtitle {
  @apply text-gray-600;
}

.header-actions {
  @apply flex items-center space-x-3;
}

/* 卡片样式 */
.card {
  @apply bg-white rounded-lg shadow-sm border;
}

.card-header {
  @apply px-6 py-4 border-b border-gray-200 flex items-center justify-between;
}

.card-title {
  @apply text-lg font-semibold text-gray-900;
}

.card-actions {
  @apply flex items-center space-x-3;
}

.card-content {
  @apply p-6;
}

/* 按钮样式 */
.btn {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
}

.btn-danger {
  @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}

.btn-sm {
  @apply px-3 py-1.5 text-xs;
}

.btn-ghost {
  @apply text-gray-600 hover:text-gray-800 hover:bg-gray-100;
}

/* 加载和空状态 */
.loading-state {
  @apply flex flex-col items-center justify-center py-12;
}

.loading-spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4;
}

.empty-state {
  @apply flex flex-col items-center justify-center py-12 text-center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .build-management {
    @apply p-4 space-y-4;
  }

  .header-content {
    @apply flex-col items-start space-y-4;
  }
}
</style>
