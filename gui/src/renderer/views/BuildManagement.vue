<!--
  构建管理页面
  对等 CLI 所有构建功能的完整实现
-->

<template>
  <div class="build-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">构建管理</h1>
          <p class="page-subtitle">管理多平台构建任务，监控构建状态和历史记录</p>
        </div>
        <div class="header-actions">
          <button
            @click="refreshData"
            :disabled="isLoading"
            class="btn btn-secondary"
          >
            <ArrowPathIcon class="w-4 h-4 mr-2" :class="{ 'animate-spin': isLoading }" />
            刷新
          </button>
          <button
            @click="showBuildConfigModal = true"
            class="btn btn-secondary"
          >
            <CogIcon class="w-4 h-4 mr-2" />
            构建配置
          </button>
          <button
            @click="showBuildModal = true"
            class="btn btn-primary"
          >
            <PlayIcon class="w-4 h-4 mr-2" />
            开始构建
          </button>
        </div>
      </div>
    </div>

    <!-- 当前构建状态 -->
    <div v-if="currentBuilds.length > 0" class="current-builds-section">
      <div class="card">
        <div class="card-header">
          <h2 class="card-title">当前构建任务</h2>
          <div class="card-actions">
            <span class="task-count">{{ currentBuilds.length }} 个任务进行中</span>
          </div>
        </div>
        <div class="card-content">
          <div class="builds-list">
            <div
              v-for="build in currentBuilds"
              :key="build.id"
              class="build-item"
            >
              <div class="build-info">
                <div class="build-header">
                  <div class="build-platform">
                    <component :is="getPlatformIcon(build.platform)" class="w-5 h-5 mr-2" />
                    <span class="platform-name">{{ getPlatformName(build.platform) }}</span>
                  </div>
                  <div class="build-type">
                    <span class="type-badge" :class="getBuildTypeClass(build.type)">
                      {{ getBuildTypeText(build.type) }}
                    </span>
                  </div>
                </div>
                <div class="build-progress">
                  <div class="progress-bar">
                    <div
                      class="progress-fill"
                      :style="{ width: `${build.progress}%` }"
                    ></div>
                  </div>
                  <span class="progress-text">{{ build.progress }}%</span>
                </div>
                <div class="build-status">
                  <span class="status-text">{{ build.statusText }}</span>
                  <span class="elapsed-time">{{ formatElapsedTime(build.startTime) }}</span>
                </div>
              </div>
              <div class="build-actions">
                <button
                  @click="showBuildLogs(build)"
                  class="btn btn-sm btn-ghost"
                >
                  <EyeIcon class="w-3 h-3 mr-1" />
                  日志
                </button>
                <button
                  @click="cancelBuild(build.id)"
                  class="btn btn-sm btn-danger"
                >
                  <XMarkIcon class="w-3 h-3 mr-1" />
                  取消
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速构建 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">快速构建</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div
          v-for="platform in platforms"
          :key="platform.id"
          class="border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors"
        >
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-3">
              <div :class="platform.iconClass" class="w-8 h-8 rounded flex items-center justify-center">
                <span class="text-white font-bold text-sm">{{ platform.icon }}</span>
              </div>
              <div>
                <h3 class="font-medium text-gray-900">{{ platform.name }}</h3>
                <p class="text-sm text-gray-500">{{ platform.description }}</p>
              </div>
            </div>
          </div>
          
          <button
            @click="startBuild(platform.id)"
            :disabled="appStore.isBuilding"
            class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors text-sm"
          >
            {{ appStore.isBuilding ? '构建中...' : '开始构建' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 构建进度 -->
    <div v-if="appStore.currentBuild" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">构建进度</h2>
      
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <span class="font-medium">{{ currentBuild.platform }} - {{ currentBuild.version }}</span>
          <span :class="getBuildStatusClass(currentBuild.status)">
            {{ getBuildStatusText(currentBuild.status) }}
          </span>
        </div>
        
        <div class="w-full bg-gray-200 rounded-full h-2">
          <div 
            class="bg-blue-600 h-2 rounded-full transition-all duration-300"
            :style="{ width: `${currentBuild.progress || 0}%` }"
          ></div>
        </div>
        
        <div class="text-sm text-gray-600">
          <p>当前步骤: {{ currentBuild.currentStep || '准备中...' }}</p>
          <p>开始时间: {{ formatDate(currentBuild.startTime) }}</p>
        </div>
        
        <!-- 构建日志 -->
        <div class="mt-4">
          <h3 class="font-medium text-gray-900 mb-2">构建日志</h3>
          <div class="bg-gray-900 text-green-400 p-4 rounded-lg h-40 overflow-y-auto font-mono text-sm">
            <div v-for="(log, index) in buildLogs" :key="index" class="mb-1">
              {{ log }}
            </div>
          </div>
        </div>
        
        <div class="flex justify-end space-x-3">
          <button
            v-if="currentBuild.status === 'running'"
            @click="cancelBuild"
            class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
          >
            取消构建
          </button>
          
          <button
            v-if="currentBuild.status === 'completed'"
            @click="clearBuild"
            class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
          >
            清除
          </button>
        </div>
      </div>
    </div>

    <!-- 构建历史 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-semibold text-gray-900">构建历史</h2>
      </div>

      <div class="divide-y divide-gray-200">
        <div
          v-for="build in buildHistory"
          :key="build.id"
          class="px-6 py-4 hover:bg-gray-50 transition-colors duration-150"
        >
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-3">
                <span class="font-medium text-gray-900">{{ build.platform }}</span>
                <span class="text-sm text-gray-500">v{{ build.version }}</span>
                <span :class="getBuildStatusClass(build.status)">
                  {{ getBuildStatusText(build.status) }}
                </span>
              </div>
              
              <div class="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                <span>{{ formatDate(build.startTime) }}</span>
                <span v-if="build.duration">耗时: {{ formatDuration(build.duration) }}</span>
                <span v-if="build.buildSize">大小: {{ build.buildSize }}</span>
              </div>
            </div>
            
            <div class="flex items-center space-x-2">
              <button
                v-if="build.status === 'success'"
                class="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                下载
              </button>
              
              <button
                class="text-gray-600 hover:text-gray-800 text-sm font-medium"
              >
                查看日志
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { useAppStore } from '../stores/app';

// Store
const appStore = useAppStore();

// 响应式数据
const isLoading = ref(false);
const isBuilding = ref(false);
const currentBuild = ref<any>(null);
const buildLogs = ref<string[]>([]);
const buildHistory = ref<any[]>([
  {
    id: '1',
    platform: 'Web Mobile',
    version: '1.0.0',
    status: 'success',
    startTime: new Date(Date.now() - 3600000).toISOString(),
    duration: 120000,
    buildSize: '3.2MB'
  },
  {
    id: '2',
    platform: 'Android',
    version: '1.0.0',
    status: 'failed',
    startTime: new Date(Date.now() - 7200000).toISOString(),
    duration: 180000
  }
]);

// 平台配置
const platforms = [
  {
    id: 'web-mobile',
    name: 'Web Mobile',
    description: 'H5 移动端',
    icon: 'H5',
    iconClass: 'bg-blue-500'
  },
  {
    id: 'android',
    name: 'Android',
    description: 'Android APK',
    icon: 'AD',
    iconClass: 'bg-green-500'
  },
  {
    id: 'ios',
    name: 'iOS',
    description: 'iOS IPA',
    icon: 'iOS',
    iconClass: 'bg-gray-500'
  },
  {
    id: 'windows',
    name: 'Windows',
    description: 'Windows EXE',
    icon: 'WIN',
    iconClass: 'bg-blue-600'
  },
  {
    id: 'mac',
    name: 'macOS',
    description: 'macOS APP',
    icon: 'MAC',
    iconClass: 'bg-gray-600'
  }
];

// 方法
const refreshData = async () => {
  isLoading.value = true;
  try {
    // 刷新构建历史
    await new Promise(resolve => setTimeout(resolve, 1000));
  } catch (error) {
    console.error('Refresh data error:', error);
  } finally {
    isLoading.value = false;
  }
};

const startBuild = async (platform: string) => {
  try {
    isBuilding.value = true;
    
    const result = await appStore.startBuild(platform);
    
    currentBuild.value = {
      id: result.buildId,
      platform: platforms.find(p => p.id === platform)?.name || platform,
      version: appStore.currentVersion?.version || '1.0.0',
      status: 'running',
      progress: 0,
      startTime: result.startTime,
      currentStep: '准备构建环境...'
    };
    
    buildLogs.value = ['[INFO] 开始构建...', '[INFO] 初始化构建环境...'];
    
    // 模拟构建进度
    simulateBuildProgress();
    
  } catch (error) {
    console.error('Start build error:', error);
    isBuilding.value = false;
  }
};

const simulateBuildProgress = () => {
  const steps = [
    { step: '加载项目配置...', progress: 10 },
    { step: '编译脚本...', progress: 30 },
    { step: '处理资源...', progress: 50 },
    { step: '打包资源...', progress: 70 },
    { step: '生成最终包...', progress: 90 },
    { step: '构建完成', progress: 100 }
  ];
  
  let currentStepIndex = 0;
  
  const interval = setInterval(() => {
    if (currentStepIndex < steps.length && currentBuild.value) {
      const step = steps[currentStepIndex];
      currentBuild.value.currentStep = step.step;
      currentBuild.value.progress = step.progress;
      
      buildLogs.value.push(`[INFO] ${step.step}`);
      
      if (step.progress === 100) {
        currentBuild.value.status = 'completed';
        buildLogs.value.push('[SUCCESS] 构建成功完成!');
        isBuilding.value = false;
        clearInterval(interval);
        
        // 添加到历史记录
        buildHistory.value.unshift({
          id: currentBuild.value.id,
          platform: currentBuild.value.platform,
          version: currentBuild.value.version,
          status: 'success',
          startTime: currentBuild.value.startTime,
          duration: Date.now() - new Date(currentBuild.value.startTime).getTime(),
          buildSize: '2.8MB'
        });
      }
      
      currentStepIndex++;
    }
  }, 2000);
};

const cancelBuild = () => {
  if (currentBuild.value) {
    currentBuild.value.status = 'cancelled';
    buildLogs.value.push('[WARN] 构建已取消');
    isBuilding.value = false;
  }
};

const clearBuild = () => {
  currentBuild.value = null;
  buildLogs.value = [];
};

const getBuildStatusClass = (status: string) => {
  switch (status) {
    case 'success':
    case 'completed':
      return 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800';
    case 'failed':
      return 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800';
    case 'running':
      return 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800';
    case 'cancelled':
      return 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800';
    default:
      return 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800';
  }
};

const getBuildStatusText = (status: string) => {
  switch (status) {
    case 'success':
    case 'completed':
      return '构建成功';
    case 'failed':
      return '构建失败';
    case 'running':
      return '构建中';
    case 'cancelled':
      return '已取消';
    default:
      return '未知状态';
  }
};

const formatDate = (dateString: string) => {
  if (!dateString) return 'N/A';
  try {
    return format(new Date(dateString), 'yyyy-MM-dd HH:mm', { locale: zhCN });
  } catch {
    return 'N/A';
  }
};

const formatDuration = (ms: number) => {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  
  if (minutes > 0) {
    return `${minutes}分${remainingSeconds}秒`;
  }
  return `${remainingSeconds}秒`;
};

// 生命周期
onMounted(() => {
  refreshData();
  
  // 监听构建进度事件
  if (window.electronAPI) {
    window.electronAPI.onBuildProgress((data: any) => {
      if (data.buildId === currentBuild.value?.id) {
        if (data.data) {
          buildLogs.value.push(data.data);
        }
        
        if (data.completed) {
          currentBuild.value.status = data.success ? 'completed' : 'failed';
          isBuilding.value = false;
        }
      }
    });
  }
});

onUnmounted(() => {
  if (window.electronAPI) {
    window.electronAPI.offBuildProgress();
  }
});
</script>
