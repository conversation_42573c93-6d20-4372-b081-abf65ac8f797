<template>
  <div class="p-6 space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">版本管理</h1>
        <p class="text-gray-600 mt-1">管理项目版本、标签、变更日志和发布</p>
      </div>
      <div class="flex space-x-3">
        <button
          @click="refreshData"
          :disabled="isLoading"
          class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          <ArrowPathIcon class="w-4 h-4 mr-2" :class="{ 'animate-spin': isLoading }" />
          刷新
        </button>

        <button
          @click="showCreateTagModal = true"
          class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <TagIcon class="w-4 h-4 mr-2" />
          创建标签
        </button>

        <button
          @click="generateChangelog"
          class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <DocumentTextIcon class="w-4 h-4 mr-2" />
          生成变更日志
        </button>

        <button
          @click="showBumpModal = true"
          class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <PlusIcon class="w-4 h-4 mr-2" />
          版本升级
        </button>

        <button
          @click="showReleaseModal = true"
          class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
        >
          <RocketLaunchIcon class="w-4 h-4 mr-2" />
          发布版本
        </button>
      </div>
    </div>

    <!-- 当前版本卡片 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-start justify-between">
        <div class="flex-1">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-semibold text-gray-900">当前版本</h2>
            <div class="flex space-x-2">
              <button
                @click="navigateToRollback"
                class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <ArrowUturnLeftIcon class="w-4 h-4 mr-2" />
                版本回滚
              </button>
            </div>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 版本信息 -->
            <div>
              <div class="flex items-center space-x-4 mb-4">
                <span class="text-3xl font-bold text-blue-600">{{ currentVersion?.version || 'N/A' }}</span>
                <div class="flex space-x-2">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    当前版本
                  </span>
                  <span v-if="currentVersion?.gitInfo?.tag" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    <TagIcon class="w-3 h-3 mr-1" />
                    {{ currentVersion.gitInfo.tag }}
                  </span>
                </div>
              </div>

              <div class="space-y-2 text-sm">
                <div class="flex items-center text-gray-600">
                  <CalendarIcon class="w-4 h-4 mr-2" />
                  创建时间: {{ formatDate(currentVersion?.lastModified) }}
                </div>
                <div v-if="currentVersion?.author" class="flex items-center text-gray-600">
                  <UserIcon class="w-4 h-4 mr-2" />
                  作者: {{ currentVersion.author }}
                </div>
                <div v-if="currentVersion?.gitInfo?.branch" class="flex items-center text-gray-600">
                  <CodeBracketIcon class="w-4 h-4 mr-2" />
                  分支: {{ currentVersion.gitInfo.branch }}
                </div>
                <div v-if="currentVersion?.gitInfo?.commit" class="flex items-center text-gray-600">
                  <HashtagIcon class="w-4 h-4 mr-2" />
                  提交: {{ currentVersion.gitInfo.commit.substring(0, 8) }}
                </div>
              </div>
            </div>

            <!-- 快速操作 -->
            <div>
              <h3 class="text-sm font-medium text-gray-900 mb-3">快速操作</h3>
              <div class="grid grid-cols-2 gap-3">
                <button
                  @click="showCreateTagModal = true"
                  class="flex flex-col items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <TagIcon class="w-5 h-5 text-blue-500 mb-1" />
                  <span class="text-xs text-gray-700">创建标签</span>
                </button>

                <button
                  @click="generateChangelog"
                  class="flex flex-col items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <DocumentTextIcon class="w-5 h-5 text-green-500 mb-1" />
                  <span class="text-xs text-gray-700">生成日志</span>
                </button>

                <button
                  @click="showReleaseModal = true"
                  class="flex flex-col items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <RocketLaunchIcon class="w-5 h-5 text-purple-500 mb-1" />
                  <span class="text-xs text-gray-700">发布版本</span>
                </button>

                <button
                  @click="showVersionDetailsModal = true"
                  class="flex flex-col items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <EyeIcon class="w-5 h-5 text-gray-500 mb-1" />
                  <span class="text-xs text-gray-700">查看详情</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 版本历史 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold text-gray-900">版本历史</h2>
          <div class="flex items-center space-x-3">
            <select
              v-model="historyFilter"
              class="text-sm border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">全部版本</option>
              <option value="major">主版本</option>
              <option value="minor">次版本</option>
              <option value="patch">补丁版本</option>
              <option value="tagged">已标记</option>
            </select>
            <button
              @click="showVersionCompareModal = true"
              class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <ArrowsRightLeftIcon class="w-4 h-4 mr-1" />
              版本对比
            </button>
          </div>
        </div>
      </div>

      <div class="divide-y divide-gray-200">
        <div
          v-for="version in filteredVersionHistory"
          :key="version.version"
          class="px-6 py-4 hover:bg-gray-50 transition-colors duration-150"
          :class="{ 'bg-blue-50': version.version === currentVersion?.version }"
        >
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-3 mb-2">
                <span class="text-lg font-semibold text-gray-900">{{ version.version }}</span>
                <div class="flex space-x-2">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {{ version.type || 'release' }}
                  </span>
                  <span
                    v-if="version.version === currentVersion?.version"
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
                  >
                    当前版本
                  </span>
                  <span
                    v-if="version.tag"
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800"
                  >
                    <TagIcon class="w-3 h-3 mr-1" />
                    {{ version.tag }}
                  </span>
                </div>
              </div>

              <p class="text-gray-600 mb-2">{{ version.message || '无描述' }}</p>

              <div class="flex items-center space-x-4 text-sm text-gray-500">
                <span class="flex items-center">
                  <CalendarIcon class="w-4 h-4 mr-1" />
                  {{ formatDate(version.date) }}
                </span>
                <span v-if="version.author" class="flex items-center">
                  <UserIcon class="w-4 h-4 mr-1" />
                  {{ version.author }}
                </span>
                <span v-if="version.commitHash" class="flex items-center">
                  <HashtagIcon class="w-4 h-4 mr-1" />
                  {{ version.commitHash.substring(0, 8) }}
                </span>
              </div>
            </div>

            <div class="flex items-center space-x-2 ml-4">
              <button
                @click="showVersionDetails(version)"
                class="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-600 hover:text-gray-800 border border-gray-300 rounded hover:bg-gray-50"
              >
                <EyeIcon class="w-3 h-3 mr-1" />
                详情
              </button>

              <button
                v-if="!version.tag"
                @click="createTagForVersion(version)"
                class="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-600 hover:text-blue-800 border border-blue-300 rounded hover:bg-blue-50"
              >
                <TagIcon class="w-3 h-3 mr-1" />
                标签
              </button>

              <button
                v-if="version.version !== currentVersion?.version"
                @click="rollbackToVersion(version.version)"
                class="inline-flex items-center px-2 py-1 text-xs font-medium text-orange-600 hover:text-orange-800 border border-orange-300 rounded hover:bg-orange-50"
              >
                <ArrowUturnLeftIcon class="w-3 h-3 mr-1" />
                回滚
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 版本升级模态框 -->
    <div v-if="showBumpModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">版本升级</h3>
        </div>
        <div class="px-6 py-4">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">升级类型</label>
              <div class="space-y-2">
                <label class="flex items-center">
                  <input v-model="bumpType" type="radio" value="patch" class="mr-2">
                  <span>Patch ({{ getNextVersion('patch') }}) - 修复更新</span>
                </label>
                <label class="flex items-center">
                  <input v-model="bumpType" type="radio" value="minor" class="mr-2">
                  <span>Minor ({{ getNextVersion('minor') }}) - 功能更新</span>
                </label>
                <label class="flex items-center">
                  <input v-model="bumpType" type="radio" value="major" class="mr-2">
                  <span>Major ({{ getNextVersion('major') }}) - 重大更新</span>
                </label>
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">更新说明</label>
              <textarea
                v-model="bumpMessage"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                rows="3"
                placeholder="请输入版本更新说明..."
              ></textarea>
            </div>
          </div>
        </div>
        <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
          <button
            @click="showBumpModal = false"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            取消
          </button>
          <button
            @click="executeBump"
            :disabled="!bumpType"
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            确认升级
          </button>
        </div>
      </div>
    </div>

    <!-- 版本回滚模态框 -->
    <div v-if="showRollbackModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">版本回滚</h3>
        </div>
        <div class="px-6 py-4">
          <p class="text-gray-600 mb-4">选择要回滚到的版本：</p>
          <div class="space-y-2 max-h-60 overflow-y-auto">
            <label
              v-for="version in availableVersions"
              :key="version.version"
              class="flex items-center p-2 border rounded hover:bg-gray-50"
            >
              <input v-model="rollbackTarget" type="radio" :value="version.version" class="mr-3">
              <div>
                <div class="font-medium">{{ version.version }}</div>
                <div class="text-sm text-gray-500">{{ formatDate(version.date) }}</div>
              </div>
            </label>
          </div>
        </div>
        <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
          <button
            @click="showRollbackModal = false"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            取消
          </button>
          <button
            @click="executeRollback"
            :disabled="!rollbackTarget"
            class="px-4 py-2 text-sm font-medium text-white bg-orange-600 rounded-md hover:bg-orange-700 disabled:opacity-50"
          >
            确认回滚
          </button>
        </div>
      </div>
    </div>

    <!-- 创建标签模态框 -->
    <div v-if="showCreateTagModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">创建 Git 标签</h3>
        </div>
        <div class="px-6 py-4">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">标签名称</label>
              <input
                v-model="tagName"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="例如: v1.0.0"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">标签描述</label>
              <textarea
                v-model="tagMessage"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                rows="3"
                placeholder="请输入标签描述..."
              ></textarea>
            </div>

            <div class="flex items-center">
              <input v-model="pushTag" type="checkbox" class="mr-2">
              <label class="text-sm text-gray-700">推送到远程仓库</label>
            </div>
          </div>
        </div>
        <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
          <button
            @click="showCreateTagModal = false"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            取消
          </button>
          <button
            @click="executeCreateTag"
            :disabled="!tagName"
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            创建标签
          </button>
        </div>
      </div>
    </div>

    <!-- 发布版本模态框 -->
    <div v-if="showReleaseModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl max-w-lg w-full mx-4">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">发布版本</h3>
        </div>
        <div class="px-6 py-4">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">发布版本</label>
              <input
                v-model="releaseVersion"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                :placeholder="currentVersion?.version"
                readonly
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">发布标题</label>
              <input
                v-model="releaseTitle"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="例如: Version 1.0.0 - 重大更新"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">发布说明</label>
              <textarea
                v-model="releaseNotes"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                rows="4"
                placeholder="请输入发布说明..."
              ></textarea>
            </div>

            <div class="space-y-2">
              <div class="flex items-center">
                <input v-model="releasePrerelease" type="checkbox" class="mr-2">
                <label class="text-sm text-gray-700">预发布版本</label>
              </div>
              <div class="flex items-center">
                <input v-model="releaseDraft" type="checkbox" class="mr-2">
                <label class="text-sm text-gray-700">保存为草稿</label>
              </div>
            </div>
          </div>
        </div>
        <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
          <button
            @click="showReleaseModal = false"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            取消
          </button>
          <button
            @click="executeRelease"
            :disabled="!releaseTitle"
            class="px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700 disabled:opacity-50"
          >
            {{ releaseDraft ? '保存草稿' : '发布版本' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 版本详情模态框 -->
    <div v-if="showVersionDetailsModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">版本详情</h3>
        </div>
        <div class="px-6 py-4">
          <div v-if="selectedVersion" class="space-y-4">
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700">版本号</label>
                <p class="text-lg font-semibold text-blue-600">{{ selectedVersion.version }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">版本类型</label>
                <p class="text-gray-900">{{ selectedVersion.type || 'release' }}</p>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700">描述</label>
              <p class="text-gray-900">{{ selectedVersion.message || '无描述' }}</p>
            </div>

            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700">创建时间</label>
                <p class="text-gray-900">{{ formatDate(selectedVersion.date) }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">作者</label>
                <p class="text-gray-900">{{ selectedVersion.author || 'Unknown' }}</p>
              </div>
            </div>

            <div v-if="selectedVersion.commitHash">
              <label class="block text-sm font-medium text-gray-700">提交哈希</label>
              <p class="text-gray-900 font-mono">{{ selectedVersion.commitHash }}</p>
            </div>

            <div v-if="selectedVersion.tag">
              <label class="block text-sm font-medium text-gray-700">Git 标签</label>
              <p class="text-gray-900">{{ selectedVersion.tag }}</p>
            </div>
          </div>
        </div>
        <div class="px-6 py-4 border-t border-gray-200 flex justify-end">
          <button
            @click="showVersionDetailsModal = false"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import {
  ArrowPathIcon,
  PlusIcon,
  TagIcon,
  DocumentTextIcon,
  RocketLaunchIcon,
  ArrowUturnLeftIcon,
  CalendarIcon,
  UserIcon,
  CodeBracketIcon,
  HashtagIcon,
  EyeIcon,
  ArrowsRightLeftIcon
} from '@heroicons/vue/24/outline';

import { useAppStore } from '../stores/app';
import { apiClient } from '../../shared/api/VersionCraftAPIClient';
import { ErrorHandler } from '../../shared/services/ErrorHandler';
import { NotificationService } from '../../shared/services/NotificationService';

// Store & Router
const appStore = useAppStore();
const router = useRouter();

// 响应式数据
const isLoading = ref(false);
const historyFilter = ref('all');

// 模态框状态
const showBumpModal = ref(false);
const showRollbackModal = ref(false);
const showCreateTagModal = ref(false);
const showReleaseModal = ref(false);
const showVersionDetailsModal = ref(false);
const showVersionCompareModal = ref(false);
const showChangelogModal = ref(false);

// 版本升级相关
const bumpType = ref<'major' | 'minor' | 'patch'>('patch');
const bumpMessage = ref('');
const rollbackTarget = ref('');

// 标签创建相关
const tagName = ref('');
const tagMessage = ref('');
const pushTag = ref(false);

// 发布相关
const releaseVersion = ref('');
const releaseTitle = ref('');
const releaseNotes = ref('');
const releasePrerelease = ref(false);
const releaseDraft = ref(false);

// 其他数据
const selectedVersion = ref<any>(null);
const changelogContent = ref('');

// 计算属性
const currentVersion = computed(() => appStore.currentVersion);
const versionHistory = computed(() => appStore.versionHistory);

const availableVersions = computed(() => {
  return versionHistory.value.filter(v => v.version !== currentVersion.value?.version);
});

// historyFilterOptions 已移除，因为未使用

const filteredVersionHistory = computed(() => {
  if (historyFilter.value === 'all') {
    return versionHistory.value;
  }

  return versionHistory.value.filter(version => {
    switch (historyFilter.value) {
      case 'major':
        return version.type === 'major';
      case 'minor':
        return version.type === 'minor';
      case 'patch':
        return version.type === 'patch';
      case 'tagged':
        return version.tag;
      default:
        return true;
    }
  });
});

// 方法
const refreshData = async () => {
  isLoading.value = true;
  try {
    await appStore.refreshAll();
  } catch (error) {
    console.error('Refresh data error:', error);
  } finally {
    isLoading.value = false;
  }
};

const formatDate = (dateString: string) => {
  if (!dateString) return 'N/A';
  try {
    return format(new Date(dateString), 'yyyy-MM-dd HH:mm', { locale: zhCN });
  } catch {
    return 'N/A';
  }
};

const getNextVersion = (type: 'major' | 'minor' | 'patch') => {
  const current = currentVersion.value?.version || '1.0.0';
  const parts = current.split('.').map(Number);
  
  switch (type) {
    case 'major':
      return `${parts[0] + 1}.0.0`;
    case 'minor':
      return `${parts[0]}.${parts[1] + 1}.0`;
    case 'patch':
      return `${parts[0]}.${parts[1]}.${parts[2] + 1}`;
    default:
      return current;
  }
};

const executeBump = async () => {
  console.log('🎯 [VersionManagement] 用户点击确认升级');
  console.log('📝 [VersionManagement] 升级参数:', {
    type: bumpType.value,
    message: bumpMessage.value
  });

  try {
    console.log('📡 [VersionManagement] 调用 appStore.bumpVersion...');
    await appStore.bumpVersion({
      type: bumpType.value,
      message: bumpMessage.value
    });

    console.log('✅ [VersionManagement] 版本升级完成，关闭模态框');
    showBumpModal.value = false;
    bumpMessage.value = '';

    // 刷新数据
    console.log('🔄 [VersionManagement] 刷新页面数据...');
    await refreshData();
    console.log('✅ [VersionManagement] 数据刷新完成');
  } catch (error) {
    console.error('💥 [VersionManagement] 版本升级失败:', error);
    // 这里应该显示错误提示给用户
    alert(`版本升级失败: ${error instanceof Error ? error.message : error}`);
  }
};

const executeRollback = async () => {
  try {
    await appStore.rollbackVersion(rollbackTarget.value);
    
    showRollbackModal.value = false;
    rollbackTarget.value = '';
    
    // 刷新数据
    await refreshData();
  } catch (error) {
    console.error('Rollback version error:', error);
  }
};

const rollbackToVersion = (version: string) => {
  // 导航到回滚管理页面，并传递目标版本
  router.push({
    path: '/rollback',
    query: { targetVersion: version }
  });
};

// 新增方法
const navigateToRollback = () => {
  router.push('/rollback');
};

const showVersionDetails = (version: any) => {
  selectedVersion.value = version;
  showVersionDetailsModal.value = true;
};

const createTagForVersion = (version: any) => {
  tagName.value = `v${version.version}`;
  tagMessage.value = version.message || `Release ${version.version}`;
  showCreateTagModal.value = true;
};

const executeCreateTag = async () => {
  try {
    const result = await apiClient.version.createTag({
      version: tagName.value,
      message: tagMessage.value,
      pushToRemote: pushTag.value
    });

    if (result.success) {
      NotificationService.success(`标签 ${tagName.value} 创建成功`);
      showCreateTagModal.value = false;

      // 重置表单
      tagName.value = '';
      tagMessage.value = '';
      pushTag.value = false;

      await refreshData();
    } else {
      throw new Error(result.error || '创建标签失败');
    }
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Create Tag');
  }
};

const generateChangelog = async () => {
  try {
    const result = await apiClient.version.generateChangelog();

    if (result.success && result.data) {
      // API 返回的是 { message: string; path: string }，我们需要读取文件内容
      // 这里先使用一个默认的变更日志内容
      changelogContent.value = `# 变更日志\n\n## 最新版本\n\n${result.data.message}\n\n生成路径: ${result.data.path}`;
      NotificationService.success('变更日志生成成功');
      showChangelogModal.value = true;
    } else {
      throw new Error(result.error || '生成变更日志失败');
    }
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Generate Changelog');
  }
};

const executeRelease = async () => {
  try {
    const result = await apiClient.version.release({
      createTag: true,
      generateChangelog: true,
      pushToRemote: true
    });

    if (result.success) {
      NotificationService.success(`版本 ${releaseVersion.value} 发布成功`);
      showReleaseModal.value = false;

      // 重置表单
      releaseVersion.value = '';
      releaseTitle.value = '';
      releaseNotes.value = '';
      releasePrerelease.value = false;
      releaseDraft.value = false;

      await refreshData();
    } else {
      throw new Error(result.error || '发布版本失败');
    }
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Create Release');
  }
};

// 事件处理
/*
const onVersionBumped = () => {
  refreshData();
};

const onTagCreated = () => {
  refreshData();
};

const onVersionReleased = () => {
  refreshData();
};
*/

// 生命周期
onMounted(() => {
  if (appStore.hasProject) {
    refreshData();
  }
});
</script>
