<!--
  增强版本管理页面
  对等 CLI 所有版本管理功能的完整实现
-->

<template>
  <div class="version-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">版本管理</h1>
          <p class="page-subtitle">管理项目版本、标签、变更日志和发布</p>
        </div>
        <div class="header-actions">
          <button
            @click="refreshData"
            :disabled="isLoading"
            class="btn btn-secondary"
          >
            <ArrowPathIcon class="w-4 h-4 mr-2" :class="{ 'animate-spin': isLoading }" />
            刷新
          </button>
          <button
            @click="showBumpModal = true"
            class="btn btn-primary"
          >
            <PlusIcon class="w-4 h-4 mr-2" />
            版本升级
          </button>
        </div>
      </div>
    </div>

    <!-- 当前版本概览 -->
    <div class="current-version-section">
      <div class="card">
        <div class="card-header">
          <h2 class="card-title">当前版本</h2>
          <div class="card-actions">
            <button
              @click="navigateToRollback"
              class="btn btn-outline btn-sm"
            >
              <ArrowUturnLeftIcon class="w-4 h-4 mr-2" />
              版本回滚
            </button>
          </div>
        </div>
        <div class="card-content">
          <div v-if="isLoading" class="loading-state">
            <div class="loading-spinner"></div>
            <p>加载版本信息...</p>
          </div>

          <div v-else-if="currentVersion" class="version-overview">
            <div class="version-main">
              <div class="version-number">
                <span class="version-text">{{ currentVersion.version }}</span>
                <div class="version-badges">
                  <span class="badge badge-success">当前版本</span>
                  <span v-if="currentVersion.gitInfo?.tag" class="badge badge-info">
                    <TagIcon class="w-3 h-3 mr-1" />
                    {{ currentVersion.gitInfo.tag }}
                  </span>
                </div>
              </div>
              <div class="version-details">
                <div class="detail-item">
                  <span class="detail-label">创建时间:</span>
                  <span class="detail-value">{{ formatDate(currentVersion.lastModified) }}</span>
                </div>
                <div v-if="currentVersion.author" class="detail-item">
                  <span class="detail-label">作者:</span>
                  <span class="detail-value">{{ currentVersion.author }}</span>
                </div>
                <div v-if="currentVersion.gitInfo?.branch" class="detail-item">
                  <span class="detail-label">分支:</span>
                  <span class="detail-value">{{ currentVersion.gitInfo.branch }}</span>
                </div>
                <div v-if="currentVersion.gitInfo?.commit" class="detail-item">
                  <span class="detail-label">提交:</span>
                  <span class="detail-value font-mono">{{ currentVersion.gitInfo.commit.substring(0, 8) }}</span>
                </div>
              </div>
            </div>

            <div class="version-actions">
              <button
                @click="showCreateTagModal = true"
                class="btn btn-secondary btn-sm"
              >
                <TagIcon class="w-4 h-4 mr-2" />
                创建标签
              </button>
              <button
                @click="generateChangelog"
                class="btn btn-secondary btn-sm"
              >
                <DocumentTextIcon class="w-4 h-4 mr-2" />
                生成变更日志
              </button>
              <button
                @click="showReleaseModal = true"
                class="btn btn-warning btn-sm"
              >
                <RocketLaunchIcon class="w-4 h-4 mr-2" />
                发布版本
              </button>
            </div>
          </div>

          <div v-else class="empty-state">
            <ExclamationTriangleIcon class="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p class="text-gray-500">无法获取版本信息</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 功能标签页 -->
    <div class="tabs-section">
      <div class="tabs-container">
        <div class="tabs-nav">
          <button
            v-for="tab in tabs"
            :key="tab.key"
            @click="activeTab = tab.key"
            :class="['tab-button', { 'tab-active': activeTab === tab.key }]"
          >
            <component :is="tab.icon" class="w-4 h-4 mr-2" />
            {{ tab.label }}
          </button>
        </div>

        <div class="tabs-content">
          <!-- 版本历史标签页 -->
          <div v-if="activeTab === 'history'" class="tab-panel">
            <div class="panel-header">
              <h3 class="panel-title">版本历史</h3>
              <div class="panel-actions">
                <VCSelect
                  v-model="historyFilter"
                  :options="historyFilterOptions"
                  placeholder="筛选版本"
                  class="filter-select"
                />
              </div>
            </div>

            <div class="version-history">
              <div v-if="isLoading" class="loading-state">
                <div class="loading-spinner"></div>
                <p>加载版本历史...</p>
              </div>

              <div v-else-if="filteredVersionHistory.length === 0" class="empty-state">
                <ClockIcon class="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p class="text-gray-500">暂无版本历史</p>
              </div>

              <div v-else class="history-timeline">
                <div
                  v-for="(version, index) in filteredVersionHistory"
                  :key="version.version"
                  class="timeline-item"
                >
                  <div class="timeline-marker" :class="getTimelineMarkerClass(version)">
                    <component :is="getVersionIcon(version)" class="w-4 h-4" />
                  </div>
                  <div class="timeline-content">
                    <div class="timeline-header">
                      <div class="version-info">
                        <span class="version-number">{{ version.version }}</span>
                        <div class="version-badges">
                          <span v-if="version.isCurrent" class="badge badge-success">当前版本</span>
                          <span v-if="version.tag" class="badge badge-info">
                            <TagIcon class="w-3 h-3 mr-1" />
                            {{ version.tag }}
                          </span>
                          <span v-if="version.commitHash" class="badge badge-gray">
                            {{ version.commitHash.substring(0, 8) }}
                          </span>
                        </div>
                      </div>
                      <div class="timeline-actions">
                        <button
                          v-if="!version.isCurrent"
                          @click="rollbackToVersion(version.version)"
                          class="btn btn-sm btn-outline"
                        >
                          回滚
                        </button>
                        <button
                          @click="showVersionDetails(version)"
                          class="btn btn-sm btn-ghost"
                        >
                          详情
                        </button>
                      </div>
                    </div>
                    <div class="timeline-details">
                      <p class="version-message">{{ version.message || '无描述' }}</p>
                      <div class="timeline-meta">
                        <span class="timestamp">{{ formatTime(version.date) }}</span>
                        <span v-if="version.author" class="author">
                          作者: {{ version.author }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

    <!-- 版本升级模态框 -->
    <div v-if="showBumpModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">版本升级</h3>
        </div>
        <div class="px-6 py-4">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">升级类型</label>
              <div class="space-y-2">
                <label class="flex items-center">
                  <input v-model="bumpType" type="radio" value="patch" class="mr-2">
                  <span>Patch ({{ getNextVersion('patch') }}) - 修复更新</span>
                </label>
                <label class="flex items-center">
                  <input v-model="bumpType" type="radio" value="minor" class="mr-2">
                  <span>Minor ({{ getNextVersion('minor') }}) - 功能更新</span>
                </label>
                <label class="flex items-center">
                  <input v-model="bumpType" type="radio" value="major" class="mr-2">
                  <span>Major ({{ getNextVersion('major') }}) - 重大更新</span>
                </label>
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">更新说明</label>
              <textarea
                v-model="bumpMessage"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                rows="3"
                placeholder="请输入版本更新说明..."
              ></textarea>
            </div>
          </div>
        </div>
        <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
          <button
            @click="showBumpModal = false"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            取消
          </button>
          <button
            @click="executeBump"
            :disabled="!bumpType"
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            确认升级
          </button>
        </div>
      </div>
    </div>

    <!-- 版本回滚模态框 -->
    <div v-if="showRollbackModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">版本回滚</h3>
        </div>
        <div class="px-6 py-4">
          <p class="text-gray-600 mb-4">选择要回滚到的版本：</p>
          <div class="space-y-2 max-h-60 overflow-y-auto">
            <label
              v-for="version in availableVersions"
              :key="version.version"
              class="flex items-center p-2 border rounded hover:bg-gray-50"
            >
              <input v-model="rollbackTarget" type="radio" :value="version.version" class="mr-3">
              <div>
                <div class="font-medium">{{ version.version }}</div>
                <div class="text-sm text-gray-500">{{ formatDate(version.date) }}</div>
              </div>
            </label>
          </div>
        </div>
        <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
          <button
            @click="showRollbackModal = false"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            取消
          </button>
          <button
            @click="executeRollback"
            :disabled="!rollbackTarget"
            class="px-4 py-2 text-sm font-medium text-white bg-orange-600 rounded-md hover:bg-orange-700 disabled:opacity-50"
          >
            确认回滚
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { useAppStore } from '../stores/app';

// Store
const appStore = useAppStore();

// 响应式数据
const isLoading = ref(false);
const showBumpModal = ref(false);
const showRollbackModal = ref(false);
const bumpType = ref<'major' | 'minor' | 'patch'>('patch');
const bumpMessage = ref('');
const rollbackTarget = ref('');

// 计算属性
const currentVersion = computed(() => appStore.currentVersion);
const versionHistory = computed(() => appStore.versionHistory);

const availableVersions = computed(() => {
  return versionHistory.value.filter(v => v.version !== currentVersion.value?.version);
});

// 方法
const refreshData = async () => {
  isLoading.value = true;
  try {
    await appStore.refreshAll();
  } catch (error) {
    console.error('Refresh data error:', error);
  } finally {
    isLoading.value = false;
  }
};

const formatDate = (dateString: string) => {
  if (!dateString) return 'N/A';
  try {
    return format(new Date(dateString), 'yyyy-MM-dd HH:mm', { locale: zhCN });
  } catch {
    return 'N/A';
  }
};

const getNextVersion = (type: 'major' | 'minor' | 'patch') => {
  const current = currentVersion.value?.version || '1.0.0';
  const parts = current.split('.').map(Number);
  
  switch (type) {
    case 'major':
      return `${parts[0] + 1}.0.0`;
    case 'minor':
      return `${parts[0]}.${parts[1] + 1}.0`;
    case 'patch':
      return `${parts[0]}.${parts[1]}.${parts[2] + 1}`;
    default:
      return current;
  }
};

const executeBump = async () => {
  console.log('🎯 [VersionManagement] 用户点击确认升级');
  console.log('📝 [VersionManagement] 升级参数:', {
    type: bumpType.value,
    message: bumpMessage.value
  });

  try {
    console.log('📡 [VersionManagement] 调用 appStore.bumpVersion...');
    await appStore.bumpVersion({
      type: bumpType.value,
      message: bumpMessage.value
    });

    console.log('✅ [VersionManagement] 版本升级完成，关闭模态框');
    showBumpModal.value = false;
    bumpMessage.value = '';

    // 刷新数据
    console.log('🔄 [VersionManagement] 刷新页面数据...');
    await refreshData();
    console.log('✅ [VersionManagement] 数据刷新完成');
  } catch (error) {
    console.error('💥 [VersionManagement] 版本升级失败:', error);
    // 这里应该显示错误提示给用户
    alert(`版本升级失败: ${error instanceof Error ? error.message : error}`);
  }
};

const executeRollback = async () => {
  try {
    await appStore.rollbackVersion(rollbackTarget.value);
    
    showRollbackModal.value = false;
    rollbackTarget.value = '';
    
    // 刷新数据
    await refreshData();
  } catch (error) {
    console.error('Rollback version error:', error);
  }
};

const rollbackToVersion = (version: string) => {
  rollbackTarget.value = version;
  showRollbackModal.value = true;
};

// 生命周期
onMounted(() => {
  if (appStore.hasProject) {
    refreshData();
  }
});
</script>
