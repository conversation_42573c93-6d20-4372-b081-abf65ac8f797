<!--
  回滚管理页面
  对等 CLI 所有回滚功能的完整实现
-->

<template>
  <div class="rollback-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">版本回滚管理</h1>
          <p class="page-subtitle">安全地回滚到历史版本，支持验证和检查点</p>
        </div>
        <div class="header-actions">
          <button 
            @click="refreshData" 
            :disabled="isLoading"
            class="btn btn-secondary"
          >
            <ArrowPathIcon class="w-4 h-4 mr-2" />
            刷新
          </button>
        </div>
      </div>
    </div>

    <!-- 快速操作面板 -->
    <div class="quick-actions-section">
      <div class="card">
        <div class="card-header">
          <h2 class="card-title">快速操作</h2>
        </div>
        <div class="card-content">
          <div class="action-buttons">
            <button 
              @click="rollbackToLast" 
              :disabled="isRollingBack || rollbackVersions.length < 2"
              :class="['btn', 'btn-warning', { 'loading': isRollingBack }]"
            >
              <ArrowUturnLeftIcon class="w-4 h-4 mr-2" />
              {{ isRollingBack ? '回滚中...' : '回滚到上一版本' }}
            </button>
            
            <button 
              @click="showCreateCheckpoint = true" 
              class="btn btn-secondary"
            >
              <BookmarkIcon class="w-4 h-4 mr-2" />
              创建检查点
            </button>
            
            <button 
              @click="showValidateModal = true" 
              class="btn btn-outline"
            >
              <ShieldCheckIcon class="w-4 h-4 mr-2" />
              验证回滚
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 可回滚版本列表 -->
    <div class="rollback-versions-section">
      <div class="card">
        <div class="card-header">
          <h2 class="card-title">可回滚版本</h2>
          <div class="card-actions">
            <span class="text-sm text-gray-500">
              共 {{ rollbackVersions.length }} 个版本
            </span>
          </div>
        </div>
        <div class="card-content">
          <div v-if="isLoading" class="loading-state">
            <div class="loading-spinner"></div>
            <p>加载版本列表...</p>
          </div>
          
          <div v-else-if="rollbackVersions.length === 0" class="empty-state">
            <ExclamationTriangleIcon class="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p class="text-gray-500">暂无可回滚版本</p>
          </div>
          
          <div v-else class="versions-table">
            <table class="table">
              <thead>
                <tr>
                  <th>版本号</th>
                  <th>创建日期</th>
                  <th>状态</th>
                  <th>描述</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr 
                  v-for="version in rollbackVersions" 
                  :key="version.version"
                  :class="{ 'current-version': version.version === currentVersion }"
                >
                  <td>
                    <div class="version-cell">
                      <span class="version-number">{{ version.version }}</span>
                      <span v-if="version.version === currentVersion" class="current-badge">
                        当前版本
                      </span>
                    </div>
                  </td>
                  <td>
                    <span class="date-text">{{ formatDate(version.date) }}</span>
                  </td>
                  <td>
                    <div class="status-badges">
                      <span 
                        :class="['badge', version.buildExists ? 'badge-success' : 'badge-error']"
                        :title="version.buildExists ? '构建可用' : '构建不可用'"
                      >
                        {{ version.buildExists ? '✓' : '✗' }}
                      </span>
                      <span 
                        v-if="version.tagExists"
                        class="badge badge-info"
                        title="Git 标签存在"
                      >
                        🏷️
                      </span>
                    </div>
                  </td>
                  <td>
                    <span class="description-text">{{ version.description || '-' }}</span>
                  </td>
                  <td>
                    <div class="action-buttons-cell">
                      <button 
                        @click="rollbackTo(version.version)"
                        :disabled="!version.buildExists || version.version === currentVersion"
                        class="btn btn-sm btn-primary"
                      >
                        回滚
                      </button>
                      <button 
                        @click="validateRollback(version.version)"
                        class="btn btn-sm btn-secondary"
                      >
                        验证
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- 回滚历史 -->
    <div class="rollback-history-section">
      <div class="card">
        <div class="card-header">
          <h2 class="card-title">回滚历史</h2>
          <div class="card-actions">
            <span class="text-sm text-gray-500">
              最近 {{ rollbackHistory.length }} 条记录
            </span>
          </div>
        </div>
        <div class="card-content">
          <div v-if="rollbackHistory.length === 0" class="empty-state">
            <ClockIcon class="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p class="text-gray-500">暂无回滚历史</p>
          </div>
          
          <div v-else class="history-timeline">
            <div 
              v-for="(record, index) in rollbackHistory" 
              :key="record.id || index"
              class="timeline-item"
            >
              <div class="timeline-marker" :class="getTimelineMarkerClass(record)">
                <component :is="getTimelineIcon(record)" class="w-4 h-4" />
              </div>
              <div class="timeline-content">
                <div class="timeline-header">
                  <div class="version-change">
                    <span class="from-version">{{ record.fromVersion }}</span>
                    <ArrowRightIcon class="w-4 h-4 mx-2 text-gray-400" />
                    <span class="to-version">{{ record.toVersion }}</span>
                  </div>
                  <div class="timeline-status">
                    <span :class="['status-badge', record.success ? 'status-success' : 'status-error']">
                      {{ record.success ? '成功' : '失败' }}
                    </span>
                  </div>
                </div>
                <div class="timeline-details">
                  <p class="reason">{{ record.reason || '手动回滚' }}</p>
                  <div class="timeline-meta">
                    <span class="timestamp">{{ formatTime(record.timestamp) }}</span>
                    <span v-if="record.rollbackBy" class="operator">
                      操作者: {{ record.rollbackBy }}
                    </span>
                    <span v-if="record.duration" class="duration">
                      耗时: {{ formatDuration(record.duration) }}
                    </span>
                  </div>
                  <div v-if="record.error" class="error-message">
                    <ExclamationCircleIcon class="w-4 h-4 mr-1 text-red-500" />
                    {{ record.error }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 模态框组件 -->
    <RollbackConfirmModal 
      v-model="showRollbackModal" 
      :target-version="targetVersion"
      :current-version="currentVersion"
      @confirm="executeRollback"
    />
    
    <CreateCheckpointModal 
      v-model="showCreateCheckpoint" 
      @success="onCheckpointCreated"
    />
    
    <ValidateRollbackModal 
      v-model="showValidateModal"
      @validate="onValidateRollback"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRoute } from 'vue-router';
import {
  ArrowPathIcon,
  ArrowUturnLeftIcon,
  BookmarkIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  ArrowRightIcon,
  ExclamationCircleIcon
} from '@heroicons/vue/24/outline';

import { apiClient } from '../../shared/api/VersionCraftAPIClient';
import { ErrorHandler } from '../../shared/services/ErrorHandler';
import { NotificationService } from '../../shared/services/NotificationService';

// 导入模态框组件（稍后创建）
import RollbackConfirmModal from '../components/modals/RollbackConfirmModal.vue';
import CreateCheckpointModal from '../components/modals/CreateCheckpointModal.vue';
import ValidateRollbackModal from '../components/modals/ValidateRollbackModal.vue';

// 路由
const route = useRoute();
// router 已移除，因为未使用

// 响应式数据
const isLoading = ref(false);
const isRollingBack = ref(false);
const rollbackVersions = ref<any[]>([]);
const rollbackHistory = ref<any[]>([]);
const currentVersion = ref('');
const targetVersion = ref('');

// 模态框状态
const showRollbackModal = ref(false);
const showCreateCheckpoint = ref(false);
const showValidateModal = ref(false);

// 计算属性
const canRollbackToLast = computed(() => {
  return rollbackVersions.value.length >= 2 && !isRollingBack.value;
});

// 生命周期
onMounted(async () => {
  await loadData();
  
  // 处理从其他页面跳转过来的预填版本
  if (route.query.targetVersion) {
    targetVersion.value = route.query.targetVersion as string;
    showRollbackModal.value = true;
  }
});

// 方法
const loadData = async () => {
  try {
    isLoading.value = true;
    
    // 并行加载数据
    const [versionsResult, historyResult, currentVersionResult] = await Promise.all([
      apiClient.rollback.list(),
      apiClient.rollback.getStatus(),
      apiClient.version.getCurrent()
    ]);
    
    if (versionsResult.success) {
      console.log('Rollback versions result:', versionsResult);
      // 后端返回的数据结构是 { versions: [...], total: number, ... }
      // 我们需要提取 versions 数组
      const data = versionsResult.data;
      if (data && data.versions) {
        rollbackVersions.value = data.versions;
      } else if (Array.isArray(data)) {
        // 如果直接返回数组，则直接使用
        rollbackVersions.value = data;
      } else {
        rollbackVersions.value = [];
      }
      console.log('Rollback versions loaded:', rollbackVersions.value);
    } else {
      console.error('Failed to load rollback versions:', versionsResult.error);
      rollbackVersions.value = [];
    }
    
    if (historyResult.success) {
      rollbackHistory.value = historyResult.data || [];
    }
    
    if (currentVersionResult.success) {
      currentVersion.value = currentVersionResult.data?.version || '';
    }
    
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Load Rollback Data');
  } finally {
    isLoading.value = false;
  }
};

const refreshData = async () => {
  await loadData();
  NotificationService.success('数据已刷新');
};

const rollbackTo = (version: string) => {
  targetVersion.value = version;
  showRollbackModal.value = true;
};

const rollbackToLast = async () => {
  if (!canRollbackToLast.value) return;
  
  try {
    isRollingBack.value = true;
    const result = await apiClient.rollback.rollbackLast();
    
    if (result.success && result.data) {
      NotificationService.success(`成功回滚到上一版本: ${result.data.toVersion}`);
      await loadData();
    } else {
      throw new Error(result.error || '回滚失败');
    }
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Rollback to Last');
  } finally {
    isRollingBack.value = false;
  }
};

const executeRollback = async (options: any) => {
  try {
    const result = await apiClient.rollback.rollbackTo(targetVersion.value, options);
    
    if (result.success && result.data) {
      NotificationService.success(`成功回滚到版本: ${result.data.toVersion}`);
      showRollbackModal.value = false;
      await loadData();
    } else {
      throw new Error(result.error || '回滚失败');
    }
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Execute Rollback');
  }
};

const validateRollback = async (version: string) => {
  try {
    const result = await apiClient.rollback.validate(version);
    
    if (result.success && result.data) {
      const validation = result.data;
      if (validation && validation.valid) {
        NotificationService.success('回滚验证通过，可以安全回滚');
      } else if (validation && validation.issues) {
        NotificationService.warning(`回滚验证失败: ${validation.issues.join(', ')}`);
      } else {
        NotificationService.warning('回滚验证失败：未知错误');
      }
    } else {
      throw new Error(result.error || '验证失败');
    }
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Validate Rollback');
  }
};

const onCheckpointCreated = (checkpoint: any) => {
  NotificationService.success(`检查点 "${checkpoint.checkpointName}" 创建成功`);
  loadData();
};

const onValidateRollback = async (version: string) => {
  await validateRollback(version);
};

// 工具方法
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN');
};

const formatTime = (timestamp: string) => {
  const now = new Date();
  const time = new Date(timestamp);
  const diff = now.getTime() - time.getTime();
  
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);
  
  if (minutes < 1) return '刚刚';
  if (minutes < 60) return `${minutes}分钟前`;
  if (hours < 24) return `${hours}小时前`;
  if (days < 7) return `${days}天前`;
  
  return time.toLocaleString('zh-CN');
};

const formatDuration = (ms: number) => {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  
  if (minutes > 0) {
    return `${minutes}分${seconds % 60}秒`;
  }
  return `${seconds}秒`;
};

const getTimelineMarkerClass = (record: any) => {
  return record.success ? 'timeline-marker-success' : 'timeline-marker-error';
};

const getTimelineIcon = (record: any) => {
  return record.success ? 'CheckCircleIcon' : 'XCircleIcon';
};
</script>

<style scoped>
/* 页面布局 */
.rollback-management {
  @apply p-6 space-y-6 max-w-7xl mx-auto;
}

/* 页面头部 */
.page-header {
  @apply bg-white rounded-lg shadow-sm border p-6;
}

.header-content {
  @apply flex items-center justify-between;
}

.header-info {
  @apply flex-1;
}

.page-title {
  @apply text-2xl font-bold text-gray-900 mb-2;
}

.page-subtitle {
  @apply text-gray-600;
}

.header-actions {
  @apply flex items-center space-x-3;
}

/* 卡片样式 */
.card {
  @apply bg-white rounded-lg shadow-sm border;
}

.card-header {
  @apply px-6 py-4 border-b border-gray-200 flex items-center justify-between;
}

.card-title {
  @apply text-lg font-semibold text-gray-900;
}

.card-actions {
  @apply flex items-center space-x-3;
}

.card-content {
  @apply p-6;
}

/* 快速操作 */
.action-buttons {
  @apply flex items-center space-x-4;
}

.action-buttons-cell {
  @apply flex items-center space-x-2;
}

/* 按钮样式 */
.btn {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
}

.btn-warning {
  @apply bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500;
}

.btn-outline {
  @apply border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500;
}

.btn-sm {
  @apply px-3 py-1.5 text-xs;
}

.btn.loading {
  @apply opacity-75 cursor-not-allowed;
}

/* 加载状态 */
.loading-state {
  @apply flex flex-col items-center justify-center py-12;
}

.loading-spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4;
}

/* 空状态 */
.empty-state {
  @apply flex flex-col items-center justify-center py-12 text-center;
}

/* 表格样式 */
.versions-table {
  @apply overflow-hidden rounded-lg border border-gray-200;
}

.table {
  @apply min-w-full divide-y divide-gray-200;
}

.table thead {
  @apply bg-gray-50;
}

.table th {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.table td {
  @apply px-6 py-4 whitespace-nowrap text-sm;
}

.table tbody tr {
  @apply bg-white hover:bg-gray-50 transition-colors;
}

.table tbody tr.current-version {
  @apply bg-blue-50 hover:bg-blue-100;
}

/* 版本单元格 */
.version-cell {
  @apply flex items-center space-x-2;
}

.version-number {
  @apply font-mono font-medium text-gray-900;
}

.current-badge {
  @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800;
}

/* 状态徽章 */
.status-badges {
  @apply flex items-center space-x-2;
}

.badge {
  @apply inline-flex items-center justify-center w-6 h-6 rounded-full text-xs font-medium;
}

.badge-success {
  @apply bg-green-100 text-green-800;
}

.badge-error {
  @apply bg-red-100 text-red-800;
}

.badge-info {
  @apply bg-blue-100 text-blue-800;
}

/* 日期和描述文本 */
.date-text {
  @apply text-gray-600;
}

.description-text {
  @apply text-gray-600 max-w-xs truncate;
}

/* 时间线样式 */
.history-timeline {
  @apply space-y-6;
}

.timeline-item {
  @apply relative flex items-start space-x-4;
}

.timeline-item:not(:last-child)::before {
  content: '';
  @apply absolute left-4 top-8 w-0.5 h-full bg-gray-200;
}

.timeline-marker {
  @apply flex items-center justify-center w-8 h-8 rounded-full border-2 bg-white;
}

.timeline-marker-success {
  @apply border-green-500 text-green-500;
}

.timeline-marker-error {
  @apply border-red-500 text-red-500;
}

.timeline-content {
  @apply flex-1 min-w-0;
}

.timeline-header {
  @apply flex items-center justify-between mb-2;
}

.version-change {
  @apply flex items-center;
}

.from-version,
.to-version {
  @apply font-mono text-sm font-medium;
}

.from-version {
  @apply text-gray-600;
}

.to-version {
  @apply text-blue-600;
}

.timeline-status {
  @apply flex items-center;
}

.status-badge {
  @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium;
}

.status-success {
  @apply bg-green-100 text-green-800;
}

.status-error {
  @apply bg-red-100 text-red-800;
}

.timeline-details {
  @apply space-y-2;
}

.reason {
  @apply text-gray-700;
}

.timeline-meta {
  @apply flex items-center space-x-4 text-xs text-gray-500;
}

.error-message {
  @apply flex items-center text-sm text-red-600 bg-red-50 p-2 rounded;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .rollback-management {
    @apply p-4 space-y-4;
  }

  .header-content {
    @apply flex-col items-start space-y-4;
  }

  .action-buttons {
    @apply flex-col space-y-2 space-x-0 w-full;
  }

  .action-buttons .btn {
    @apply w-full justify-center;
  }

  .versions-table {
    @apply overflow-x-auto;
  }

  .table {
    @apply min-w-max;
  }
}
</style>
