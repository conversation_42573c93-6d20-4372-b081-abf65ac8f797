import { app, BrowserWindow, ipcMain, dialog, shell } from 'electron';
import { join } from 'path';
import { spawn } from 'child_process';
import Store from 'electron-store';
import fs from 'fs-extra';
import { VersionCraftService } from './services/VersionCraftService';
import { ProjectService } from './services/ProjectService';
import { IPCHandlers } from './ipc/handlers';
import { IPC_COMMANDS } from '../constants/ipc-commands';

// 配置存储
const store = new Store();

// 服务实例
let versionCraftService: VersionCraftService;
let projectService: ProjectService;

// 主窗口
let mainWindow: BrowserWindow | null = null;

// 开发环境检测
const isDev = process.env.NODE_ENV === 'development';

// 创建主窗口
function createMainWindow() {
  mainWindow = new BrowserWindow({
    width: 1000,
    height: 700,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: join(__dirname, '../main/preload.js')
    },
    titleBarStyle: 'default',
    show: false, // 先隐藏，加载完成后显示
    icon: join(__dirname, '../../assets/icon.png') // 应用图标
  });

  // 加载页面
  if (isDev) {
    mainWindow.loadURL('http://localhost:5173');
    mainWindow.webContents.openDevTools();
  } else {
    // 在生产环境中，__dirname 指向 dist/main，所以需要向上一级到达 dist，然后进入 renderer
    const htmlPath = join(__dirname, '../renderer/index.html');
    console.log('Loading HTML from:', htmlPath);
    mainWindow.loadFile(htmlPath);
  }

  // 窗口准备好后显示
  mainWindow.once('ready-to-show', () => {
    mainWindow?.show();
    
    // 恢复窗口状态
    const windowState = store.get('windowState') as any;
    if (windowState) {
      mainWindow?.setBounds(windowState);
      if (windowState.isMaximized) {
        mainWindow?.maximize();
      }
    }
  });

  // 保存窗口状态
  mainWindow.on('close', () => {
    if (mainWindow) {
      const bounds = mainWindow.getBounds();
      const isMaximized = mainWindow.isMaximized();
      store.set('windowState', { ...bounds, isMaximized });
    }
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// 应用准备就绪
app.whenReady().then(() => {
  createMainWindow();
  
  // 初始化服务
  const currentProject = store.get('currentProject') as string;
  if (currentProject && fs.existsSync(currentProject)) {
    versionCraftService = new VersionCraftService(currentProject);
    projectService = new ProjectService(currentProject);

    // 注册统一的 IPC 处理器
    IPCHandlers.register(versionCraftService);
  }

  // macOS 特殊处理
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createMainWindow();
    }
  });
});

// 所有窗口关闭时退出应用 (macOS 除外)
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// IPC 事件处理

// 选择项目目录
ipcMain.handle(IPC_COMMANDS.PROJECT.SELECT_DIRECTORY, async () => {
  const result = await dialog.showOpenDialog(mainWindow!, {
    properties: ['openDirectory'],
    title: '选择 Version-Craft 项目目录'
  });

  if (!result.canceled && result.filePaths.length > 0) {
    const projectPath = result.filePaths[0];
    
    // 验证是否为有效的 Version-Craft 项目
    const configPath = join(projectPath, 'version-craft.config.json');
    if (await fs.pathExists(configPath)) {
      // 保存当前项目路径
      store.set('currentProject', projectPath);
      
      // 初始化服务
      versionCraftService = new VersionCraftService(projectPath);
      projectService = new ProjectService(projectPath);

      // 注册统一的 IPC 处理器
      IPCHandlers.register(versionCraftService);
      
      return { success: true, path: projectPath };
    } else {
      return { 
        success: false, 
        error: '所选目录不是有效的 Version-Craft 项目' 
      };
    }
  }

  return { success: false, error: '未选择目录' };
});

// 获取当前项目路径
ipcMain.handle(IPC_COMMANDS.PROJECT.GET_CURRENT, () => {
  return store.get('currentProject') || null;
});

// 获取项目信息
ipcMain.handle(IPC_COMMANDS.PROJECT.GET_INFO, async () => {
  if (!projectService) {
    return { success: false, error: '未选择项目' };
  }

  try {
    const info = await projectService.getProjectInfo();
    return { success: true, data: info };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : '获取项目信息失败' 
    };
  }
});

// 获取当前版本 - 已迁移到统一的 IPC 处理器 (IPCHandlers.registerVersionHandlers)
// 旧的处理器已删除，使用新的 version:get-current 命名空间

// 获取版本历史 - 已迁移到统一的 IPC 处理器 (IPCHandlers.registerVersionHandlers)
// 旧的处理器已删除，使用新的 version:get-history 命名空间

// 版本升级 - 已迁移到统一的 IPC 处理器 (IPCHandlers.registerVersionHandlers)
// 旧的处理器已删除，使用新的 version:bump 命名空间

// 版本回滚 - 已迁移到统一的 IPC 处理器 (IPCHandlers.registerRollbackHandlers)
// 旧的处理器已删除，使用新的 rollback:* 命名空间

// 开始构建 - 已迁移到统一的 IPC 处理器 (IPCHandlers.registerBuildHandlers)
// 旧的处理器已删除，使用新的 build:start 命名空间

// 取消构建 - 已迁移到统一的 IPC 处理器 (IPCHandlers.registerBuildHandlers)
// 旧的处理器已删除，使用新的 build:cancel 命名空间

// ==================== 热更新相关 IPC 处理 ====================
// 已迁移到统一的 IPC 处理器 (IPCHandlers.registerHotUpdateHandlers)
// 旧的处理器已删除，使用新的 hotupdate:* 命名空间

// 生成增量更新包 - 已迁移到 hotupdate:create-patch

// 验证资源清单 - 已迁移到 hotupdate:verify

// 获取热更新历史 - 已迁移到统一处理器

// 所有热更新相关的旧处理器已迁移到统一的 IPC 处理器
// 使用新的 hotupdate:* 命名空间：
// - hotupdate-check-changes → hotupdate:check-changes
// - hotupdate-clean-cache → hotupdate:clean
// - hotupdate-get-config → hotupdate:get-config
// - hotupdate-set-config → hotupdate:get-config

// 获取热更新统计 - 已迁移到统一处理器

// 打开外部链接
ipcMain.handle(IPC_COMMANDS.SYSTEM.OPEN_EXTERNAL, async (event, url) => {
  await shell.openExternal(url);
});

// 显示文件夹
ipcMain.handle(IPC_COMMANDS.SYSTEM.SHOW_ITEM_IN_FOLDER, async (event, path) => {
  shell.showItemInFolder(path);
});

// 退出应用
ipcMain.handle(IPC_COMMANDS.SYSTEM.QUIT_APP, () => {
  app.quit();
});

// 错误处理
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
