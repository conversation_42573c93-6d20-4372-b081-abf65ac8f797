import { app, BrowserWindow, ipcMain, dialog, shell } from 'electron';
import { join } from 'path';
import { spawn } from 'child_process';
import Store from 'electron-store';
import fs from 'fs-extra';
import { VersionCraftService } from './services/VersionCraftService';
import { ProjectService } from './services/ProjectService';
import { IPCHandlers } from './ipc/handlers';

// 配置存储
const store = new Store();

// 服务实例
let versionCraftService: VersionCraftService;
let projectService: ProjectService;

// 主窗口
let mainWindow: BrowserWindow | null = null;

// 开发环境检测
const isDev = process.env.NODE_ENV === 'development';

// 创建主窗口
function createMainWindow() {
  mainWindow = new BrowserWindow({
    width: 1000,
    height: 700,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: join(__dirname, '../main/preload.js')
    },
    titleBarStyle: 'default',
    show: false, // 先隐藏，加载完成后显示
    icon: join(__dirname, '../../assets/icon.png') // 应用图标
  });

  // 加载页面
  if (isDev) {
    mainWindow.loadURL('http://localhost:5173');
    mainWindow.webContents.openDevTools();
  } else {
    // 在生产环境中，__dirname 指向 dist/main，所以需要向上一级到达 dist，然后进入 renderer
    const htmlPath = join(__dirname, '../renderer/index.html');
    console.log('Loading HTML from:', htmlPath);
    mainWindow.loadFile(htmlPath);
  }

  // 窗口准备好后显示
  mainWindow.once('ready-to-show', () => {
    mainWindow?.show();
    
    // 恢复窗口状态
    const windowState = store.get('windowState') as any;
    if (windowState) {
      mainWindow?.setBounds(windowState);
      if (windowState.isMaximized) {
        mainWindow?.maximize();
      }
    }
  });

  // 保存窗口状态
  mainWindow.on('close', () => {
    if (mainWindow) {
      const bounds = mainWindow.getBounds();
      const isMaximized = mainWindow.isMaximized();
      store.set('windowState', { ...bounds, isMaximized });
    }
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// 应用准备就绪
app.whenReady().then(() => {
  createMainWindow();
  
  // 初始化服务
  const currentProject = store.get('currentProject') as string;
  if (currentProject && fs.existsSync(currentProject)) {
    versionCraftService = new VersionCraftService(currentProject);
    projectService = new ProjectService(currentProject);
  }

  // macOS 特殊处理
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createMainWindow();
    }
  });
});

// 所有窗口关闭时退出应用 (macOS 除外)
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// IPC 事件处理

// 选择项目目录
ipcMain.handle('select-project-directory', async () => {
  const result = await dialog.showOpenDialog(mainWindow!, {
    properties: ['openDirectory'],
    title: '选择 Version-Craft 项目目录'
  });

  if (!result.canceled && result.filePaths.length > 0) {
    const projectPath = result.filePaths[0];
    
    // 验证是否为有效的 Version-Craft 项目
    const configPath = join(projectPath, 'version-craft.config.json');
    if (await fs.pathExists(configPath)) {
      // 保存当前项目路径
      store.set('currentProject', projectPath);
      
      // 初始化服务
      versionCraftService = new VersionCraftService(projectPath);
      projectService = new ProjectService(projectPath);
      
      return { success: true, path: projectPath };
    } else {
      return { 
        success: false, 
        error: '所选目录不是有效的 Version-Craft 项目' 
      };
    }
  }

  return { success: false, error: '未选择目录' };
});

// 获取当前项目路径
ipcMain.handle('get-current-project', () => {
  return store.get('currentProject') || null;
});

// 获取项目信息
ipcMain.handle('get-project-info', async () => {
  if (!projectService) {
    return { success: false, error: '未选择项目' };
  }

  try {
    const info = await projectService.getProjectInfo();
    return { success: true, data: info };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : '获取项目信息失败' 
    };
  }
});

// 获取当前版本
ipcMain.handle('get-current-version', async () => {
  if (!versionCraftService) {
    return { success: false, error: '未选择项目' };
  }

  try {
    const version = await versionCraftService.version.getCurrentVersion();
    return { success: true, data: version };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : '获取当前版本失败'
    };
  }
});

// 获取版本历史
ipcMain.handle('get-version-history', async () => {
  if (!versionCraftService) {
    return { success: false, error: '未选择项目' };
  }

  try {
    const history = await versionCraftService.version.getVersionHistory();
    return { success: true, data: history };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : '获取版本历史失败'
    };
  }
});

// 版本升级
ipcMain.handle('bump-version', async (event, options) => {
  console.log('🚀 [IPC] 收到版本升级请求:', options);

  if (!versionCraftService) {
    console.error('❌ [IPC] 版本升级失败: 未选择项目');
    return { success: false, error: '未选择项目' };
  }

  try {
    console.log('📝 [IPC] 开始执行版本升级...');
    const result = await versionCraftService.version.bumpVersion(options);
    // 通知版本变更
    versionCraftService.notifyVersionChanged();
    console.log('✅ [IPC] 版本升级成功:', result);
    return { success: true, data: result };
  } catch (error) {
    console.error('❌ [IPC] 版本升级失败:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '版本升级失败'
    };
  }
});

// 版本回滚
ipcMain.handle('rollback-version', async (event, targetVersion, force = false) => {
  if (!versionCraftService) {
    return { success: false, error: '未选择项目' };
  }

  try {
    const result = await versionCraftService.version.rollbackVersion(targetVersion, force);
    // 通知版本变更
    versionCraftService.notifyVersionChanged();
    return { success: true, data: result };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : '版本回滚失败' 
    };
  }
});

// 开始构建
ipcMain.handle('start-build', async (event, platform, options = {}) => {
  if (!versionCraftService) {
    return { success: false, error: '未选择项目' };
  }

  try {
    const buildId = `build_${Date.now()}_${platform}`;
    const startTime = new Date().toISOString();

    // 发送构建开始事件
    event.sender.send('build-progress', {
      buildId,
      progress: 0,
      step: '初始化构建环境...',
      message: '开始构建流程'
    });

    // 启动构建（异步）
    versionCraftService.build.startBuild(platform, { ...options, buildId })
      .then(result => {
        // 构建成功
        event.sender.send('build-complete', {
          buildId,
          success: true,
          outputPath: result.outputPath,
          buildSize: result.buildSize,
          duration: result.duration
        });
      })
      .catch(error => {
        // 构建失败
        event.sender.send('build-complete', {
          buildId,
          success: false,
          error: error instanceof Error ? error.message : '构建失败'
        });
      });

    return {
      success: true,
      data: {
        buildId,
        platform,
        startTime,
        message: '构建已启动'
      }
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : '启动构建失败'
    };
  }
});

// 取消构建
ipcMain.handle('cancel-build', async (event, buildId) => {
  if (!versionCraftService) {
    return { success: false, error: '未选择项目' };
  }

  try {
    // 这里应该调用构建服务的取消方法
    // await versionCraftService.build.cancelBuild(buildId);

    // 发送取消事件
    event.sender.send('build-complete', {
      buildId,
      success: false,
      cancelled: true,
      error: '构建已被用户取消'
    });

    return { success: true, message: '构建已取消' };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : '取消构建失败'
    };
  }
});

// ==================== 热更新相关 IPC 处理 ====================

// 生成热更新资源清单
ipcMain.handle('hotupdate-generate-manifest', async (event, options = {}) => {
  if (!versionCraftService) {
    return { success: false, error: '未选择项目' };
  }

  try {
    const result = await versionCraftService.hotupdate.generateManifest(options);
    return { success: true, data: result };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : '生成资源清单失败'
    };
  }
});

// 生成增量更新包
ipcMain.handle('hotupdate-generate-patch', async (event, options) => {
  if (!versionCraftService) {
    return { success: false, error: '未选择项目' };
  }

  try {
    const result = await versionCraftService.hotupdate.generatePatch(options);
    return { success: true, data: result };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : '生成增量更新包失败'
    };
  }
});

// 验证资源清单
ipcMain.handle('hotupdate-verify-manifest', async (event, manifestPath) => {
  if (!versionCraftService) {
    return { success: false, error: '未选择项目' };
  }

  try {
    const result = await versionCraftService.hotupdate.verifyManifest(manifestPath);
    return { success: true, data: result };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : '验证资源清单失败'
    };
  }
});

// 获取热更新历史
ipcMain.handle('hotupdate-get-history', async (event, platform) => {
  if (!versionCraftService) {
    return { success: false, error: '未选择项目' };
  }

  try {
    const result = await versionCraftService.hotupdate.getUpdateHistory(platform);
    return { success: true, data: result };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : '获取热更新历史失败'
    };
  }
});

// 检查资源变更
ipcMain.handle('hotupdate-check-changes', async (event, fromVersion, toVersion) => {
  if (!versionCraftService) {
    return { success: false, error: '未选择项目' };
  }

  try {
    const result = await versionCraftService.hotupdate.checkResourceChanges(fromVersion, toVersion);
    return { success: true, data: result };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : '检查资源变更失败'
    };
  }
});

// 清理热更新缓存
ipcMain.handle('hotupdate-clean-cache', async (event) => {
  if (!versionCraftService) {
    return { success: false, error: '未选择项目' };
  }

  try {
    const result = await versionCraftService.hotupdate.cleanCache();
    return { success: true, data: result };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : '清理缓存失败'
    };
  }
});

// 获取热更新配置
ipcMain.handle('hotupdate-get-config', async (event) => {
  if (!versionCraftService) {
    return { success: false, error: '未选择项目' };
  }

  try {
    const result = await versionCraftService.hotupdate.getHotUpdateConfig();
    return { success: true, data: result };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : '获取热更新配置失败'
    };
  }
});

// 设置热更新配置
ipcMain.handle('hotupdate-set-config', async (event, config) => {
  if (!versionCraftService) {
    return { success: false, error: '未选择项目' };
  }

  try {
    const result = await versionCraftService.hotupdate.setHotUpdateConfig(config);
    return { success: true, data: result };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : '设置热更新配置失败'
    };
  }
});

// 获取热更新统计
ipcMain.handle('hotupdate-get-stats', async (event) => {
  if (!versionCraftService) {
    return { success: false, error: '未选择项目' };
  }

  try {
    const result = await versionCraftService.hotupdate.getHotUpdateStats();
    return { success: true, data: result };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : '获取热更新统计失败'
    };
  }
});

// 打开外部链接
ipcMain.handle('open-external', async (event, url) => {
  await shell.openExternal(url);
});

// 显示文件夹
ipcMain.handle('show-item-in-folder', async (event, path) => {
  shell.showItemInFolder(path);
});

// 退出应用
ipcMain.handle('quit-app', () => {
  app.quit();
});

// 错误处理
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
