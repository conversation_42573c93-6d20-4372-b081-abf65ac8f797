/**
 * 统一的 IPC 处理器
 * 处理所有来自渲染进程的 IPC 调用
 */

import { ipcMain } from 'electron';
import type { APIResponse } from '../../shared/types/api';
import { VersionCraftService } from '../services/VersionCraftService';

// ==================== IPC 处理器类 ====================

export class IPCHandlers {
  private static versionCraftService: VersionCraftService | null = null;

  /**
   * 注册所有 IPC 处理器
   */
  static register(versionCraftService: VersionCraftService): void {
    this.versionCraftService = versionCraftService;

    // 版本管理处理器
    this.registerVersionHandlers();
    
    // 构建管理处理器
    this.registerBuildHandlers();
    
    // 回滚管理处理器
    this.registerRollbackHandlers();
    
    // 热更新管理处理器
    this.registerHotUpdateHandlers();
    
    // 部署管理处理器
    this.registerDeployHandlers();
    
    // 配置管理处理器
    this.registerConfigHandlers();
    
    // 系统管理处理器
    this.registerSystemHandlers();

    console.log('✅ All IPC handlers registered');
  }

  // ==================== 版本管理处理器 ====================

  private static registerVersionHandlers(): void {
    ipcMain.handle('version:get-current', this.handleVersionGetCurrent.bind(this));
    ipcMain.handle('version:bump', this.handleVersionBump.bind(this));
    ipcMain.handle('version:get-history', this.handleVersionGetHistory.bind(this));
    ipcMain.handle('version:create-tag', this.handleVersionCreateTag.bind(this));
    ipcMain.handle('version:generate-changelog', this.handleVersionGenerateChangelog.bind(this));
    ipcMain.handle('version:release', this.handleVersionRelease.bind(this));
    ipcMain.handle('version:get-formatted-list', this.handleVersionGetFormattedList.bind(this));
  }

  private static async handleVersionGetCurrent(): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.version.getCurrentVersion();
      return { success: true, data: result };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleVersionBump(event: any, options: any): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.version.bumpVersion(options);
      return { success: true, data: result };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleVersionGetHistory(): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.version.getVersionHistory();
      return { success: true, data: result };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleVersionCreateTag(event: any, options: any): Promise<APIResponse> {
    try {
      // 这里需要实现标签创建逻辑
      // 暂时返回成功响应
      return { 
        success: true, 
        data: { 
          message: `标签 ${options.version || 'current'} 创建成功`,
          tag: options.version || 'current'
        } 
      };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleVersionGenerateChangelog(event: any, options?: any): Promise<APIResponse> {
    try {
      // 这里需要实现变更日志生成逻辑
      // 暂时返回成功响应
      return { 
        success: true, 
        data: { 
          message: '变更日志生成成功',
          path: './CHANGELOG.md'
        } 
      };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleVersionRelease(event: any, options?: any): Promise<APIResponse> {
    try {
      // 这里需要实现版本发布逻辑
      // 暂时返回成功响应
      return { 
        success: true, 
        data: { 
          message: '版本发布成功',
          versionInfo: { version: '1.0.0' }
        } 
      };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleVersionGetFormattedList(): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.version.getVersionHistory();
      // 转换为格式化列表格式
      const formattedList = result.map((item: any) => ({
        version: item.version,
        isCurrent: item.isCurrent || false
      }));
      return { success: true, data: formattedList };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  // ==================== 构建管理处理器 ====================

  private static registerBuildHandlers(): void {
    ipcMain.handle('build:start', this.handleBuildStart.bind(this));
    ipcMain.handle('build:cancel', this.handleBuildCancel.bind(this));
    ipcMain.handle('build:get-stats', this.handleBuildGetStats.bind(this));
    ipcMain.handle('build:clean', this.handleBuildClean.bind(this));
    ipcMain.handle('build:get-history', this.handleBuildGetHistory.bind(this));
    ipcMain.handle('build:build-all', this.handleBuildAll.bind(this));
    ipcMain.handle('build:build-web', this.handleBuildWeb.bind(this));
    ipcMain.handle('build:build-android', this.handleBuildAndroid.bind(this));
    ipcMain.handle('build:build-ios', this.handleBuildIOS.bind(this));
    ipcMain.handle('build:build-windows', this.handleBuildWindows.bind(this));
    ipcMain.handle('build:build-mac', this.handleBuildMac.bind(this));
  }

  private static async handleBuildStart(event: any, platform: string, options?: any): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.build.startBuild(platform, options);
      return { success: true, data: result };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleBuildCancel(event: any, buildId: string): Promise<APIResponse> {
    try {
      // 这里需要实现构建取消逻辑
      return { 
        success: true, 
        data: { message: `构建 ${buildId} 已取消` } 
      };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleBuildGetStats(): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.build.getBuildStats();
      return { success: true, data: result };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleBuildClean(): Promise<APIResponse> {
    try {
      // 这里需要实现构建清理逻辑
      return { 
        success: true, 
        data: { message: '构建输出已清理' } 
      };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleBuildGetHistory(): Promise<APIResponse> {
    try {
      // 这里需要实现构建历史获取逻辑
      return { 
        success: true, 
        data: [] // 暂时返回空数组
      };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleBuildAll(event: any, options?: any): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.build.buildAllPlatforms(options);
      return { success: true, data: result };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleBuildWeb(event: any, options?: any): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.build.buildPlatform('web-mobile', options);
      return { success: true, data: result };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleBuildAndroid(event: any, options?: any): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.build.buildPlatform('android', options);
      return { success: true, data: result };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleBuildIOS(event: any, options?: any): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.build.buildPlatform('ios', options);
      return { success: true, data: result };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleBuildWindows(event: any, options?: any): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.build.buildPlatform('win32', options);
      return { success: true, data: result };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleBuildMac(event: any, options?: any): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.build.buildPlatform('mac', options);
      return { success: true, data: result };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  // ==================== 回滚管理处理器 ====================

  private static registerRollbackHandlers(): void {
    ipcMain.handle('rollback:list', (...args) => {
      console.log('🔍 [IPC] rollback:list handler called with args:', args);
      return this.handleRollbackList.bind(this)(...args);
    });
    ipcMain.handle('rollback:rollback-to', this.handleRollbackTo.bind(this));
    ipcMain.handle('rollback:rollback-last', this.handleRollbackLast.bind(this));
    ipcMain.handle('rollback:get-status', (...args) => {
      console.log('🔍 [IPC] rollback:get-status handler called with args:', args);
      return this.handleRollbackGetStatus.bind(this)(...args);
    });
    ipcMain.handle('rollback:validate', this.handleRollbackValidate.bind(this));
    ipcMain.handle('rollback:create-checkpoint', this.handleRollbackCreateCheckpoint.bind(this));
  }

  private static async handleRollbackList(): Promise<APIResponse> {
    console.log('🔍 [IPC] handleRollbackList called');
    try {
      console.log('🔍 [IPC] Calling versionCraftService.rollback.getRollbackVersions()...');
      const result = await this.versionCraftService!.rollback.getRollbackVersions();
      console.log('🔍 [IPC] getRollbackVersions result:', result);
      return { success: true, data: result };
    } catch (error) {
      console.error('❌ [IPC] handleRollbackList error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleRollbackTo(event: any, version: string, options?: any): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.rollback.rollbackToVersion(version, options);
      return { success: true, data: result };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleRollbackLast(event: any, options?: any): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.rollback.rollbackToLastVersion(options);
      return { success: true, data: result };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleRollbackGetStatus(): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.rollback.getRollbackHistory();
      return { success: true, data: result };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleRollbackValidate(event: any, version: string): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.rollback.validateRollback(version);
      return { success: true, data: result };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleRollbackCreateCheckpoint(event: any, name?: string): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.rollback.createRollbackCheckpoint(name);
      return { success: true, data: result };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  // ==================== 热更新管理处理器 ====================

  private static registerHotUpdateHandlers(): void {
    ipcMain.handle('hotupdate:generate-manifest', this.handleHotUpdateGenerateManifest.bind(this));
    ipcMain.handle('hotupdate:create-patch', this.handleHotUpdateCreatePatch.bind(this));
    ipcMain.handle('hotupdate:verify', this.handleHotUpdateVerify.bind(this));
    ipcMain.handle('hotupdate:clean', this.handleHotUpdateClean.bind(this));
    ipcMain.handle('hotupdate:compare', this.handleHotUpdateCompare.bind(this));
    ipcMain.handle('hotupdate:get-config', this.handleHotUpdateGetConfig.bind(this));
    ipcMain.handle('hotupdate:check-changes', this.handleHotUpdateCheckChanges.bind(this));
  }

  private static async handleHotUpdateGenerateManifest(event: any, options?: any): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.hotupdate.generateManifest(options);
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleHotUpdateCreatePatch(event: any, options: any): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.hotupdate.createPatch(options.fromVersion, options.toVersion);
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleHotUpdateVerify(event: any, options: any): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.hotupdate.verifyManifest(options.manifestPath);
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleHotUpdateClean(event: any, options?: any): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.hotupdate.cleanOldVersions(options);
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleHotUpdateCompare(event: any, options: any): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.hotupdate.compareVersions(options.version1, options.version2);
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleHotUpdateGetConfig(): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.hotupdate.getHotUpdateConfig();
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleHotUpdateCheckChanges(event: any, fromVersion: string, toVersion: string): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.hotupdate.compareVersions(fromVersion, toVersion);
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  // ==================== 部署管理处理器 ====================

  private static registerDeployHandlers(): void {
    ipcMain.handle('deploy:to-staging', this.handleDeployToStaging.bind(this));
    ipcMain.handle('deploy:to-production', this.handleDeployToProduction.bind(this));
    ipcMain.handle('deploy:get-history', this.handleDeployGetHistory.bind(this));
    ipcMain.handle('deploy:get-status', this.handleDeployGetStatus.bind(this));
    ipcMain.handle('deploy:rollback', this.handleDeployRollback.bind(this));
    ipcMain.handle('deploy:cancel', this.handleDeployCancel.bind(this));
  }

  private static async handleDeployToStaging(event: any, platform?: string): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.deploy.deployToEnvironment('staging', platform);
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleDeployToProduction(event: any, platform?: string): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.deploy.deployToEnvironment('production', platform);
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleDeployGetHistory(): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.deploy.getDeploymentHistory();
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleDeployGetStatus(): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.deploy.getDeploymentStatus();
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleDeployRollback(event: any, deploymentId: string): Promise<APIResponse> {
    try {
      // 这里需要实现部署回滚逻辑
      return {
        success: true,
        data: { message: `部署 ${deploymentId} 回滚成功` }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleDeployCancel(event: any, deploymentId: string): Promise<APIResponse> {
    try {
      // 这里需要实现部署取消逻辑
      return {
        success: true,
        data: { message: `部署 ${deploymentId} 已取消` }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  // ==================== 配置管理处理器 ====================

  private static registerConfigHandlers(): void {
    ipcMain.handle('config:get', this.handleConfigGet.bind(this));
    ipcMain.handle('config:set', this.handleConfigSet.bind(this));
    ipcMain.handle('config:validate', this.handleConfigValidate.bind(this));
    ipcMain.handle('config:reset', this.handleConfigReset.bind(this));
    ipcMain.handle('config:export', this.handleConfigExport.bind(this));
    ipcMain.handle('config:import', this.handleConfigImport.bind(this));
    ipcMain.handle('config:init', this.handleConfigInit.bind(this));
  }

  private static async handleConfigGet(): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.config.getConfig();
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleConfigSet(event: any, key: string, value: any): Promise<APIResponse> {
    try {
      await this.versionCraftService!.config.setConfig(key, value);
      return {
        success: true,
        data: { message: `配置项 ${key} 设置成功` }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleConfigValidate(): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.config.validateConfig();
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleConfigReset(): Promise<APIResponse> {
    try {
      // 这里需要实现配置重置逻辑
      return {
        success: true,
        data: { message: '配置已重置为默认值' }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleConfigExport(event: any, options: any): Promise<APIResponse> {
    try {
      // 这里需要实现配置导出逻辑
      return {
        success: true,
        data: {
          message: '配置导出成功',
          path: options.filePath
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleConfigImport(event: any, options: any): Promise<APIResponse> {
    try {
      // 这里需要实现配置导入逻辑
      return {
        success: true,
        data: { message: '配置导入成功' }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleConfigInit(): Promise<APIResponse> {
    try {
      // 这里需要实现配置初始化逻辑
      return {
        success: true,
        data: { message: '配置初始化成功' }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  // ==================== 系统管理处理器 ====================

  private static registerSystemHandlers(): void {
    ipcMain.handle('system:health-check', this.handleSystemHealthCheck.bind(this));
    ipcMain.handle('system:get-service-info', this.handleSystemGetServiceInfo.bind(this));
    ipcMain.handle('system:get-project-info', this.handleSystemGetProjectInfo.bind(this));
    ipcMain.handle('system:set-project-path', this.handleSystemSetProjectPath.bind(this));
    ipcMain.handle('system:get-logs', this.handleSystemGetLogs.bind(this));
  }

  private static async handleSystemHealthCheck(): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.healthCheck();
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleSystemGetServiceInfo(): Promise<APIResponse> {
    try {
      const result = this.versionCraftService!.getServiceInfo();
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleSystemGetProjectInfo(): Promise<APIResponse> {
    try {
      // 这里需要实现项目信息获取逻辑
      return {
        success: true,
        data: {
          name: 'Version-Craft Project',
          path: this.versionCraftService!['projectPath'] || process.cwd(),
          version: '1.0.0'
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleSystemSetProjectPath(event: any, path: string): Promise<APIResponse> {
    try {
      // 这里需要实现项目路径设置逻辑
      return {
        success: true,
        data: { message: `项目路径设置为: ${path}` }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleSystemGetLogs(event: any, level?: string, limit?: number): Promise<APIResponse> {
    try {
      // 这里需要实现日志获取逻辑
      return {
        success: true,
        data: [] // 暂时返回空数组
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }
}
