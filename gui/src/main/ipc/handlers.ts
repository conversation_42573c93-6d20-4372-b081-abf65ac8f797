/**
 * 统一的 IPC 处理器
 * 处理所有来自渲染进程的 IPC 调用
 */

import { ipcMain, dialog, shell, app } from 'electron';
import { join } from 'path';
import * as fs from 'fs-extra';
import Store from 'electron-store';
import type { APIResponse } from '../../shared/types/api';
import { VersionCraftService } from '../services/VersionCraftService';
import { ProjectService } from '../services/ProjectService';
import { IPC_COMMANDS } from '../../shared/constants/ipc-commands';

// 初始化存储
const store = new Store();

// ==================== IPC 处理器类 ====================

export class IPCHandlers {
  private static versionCraftService: VersionCraftService | null = null;
  private static projectService: ProjectService | null = null;

  /**
   * 注册基础 IPC 处理器（不需要项目服务的处理器）
   */
  static registerBasicHandlers(): void {
    // 日志管理处理器
    this.registerLoggerHandlers();

    // 项目管理处理器
    this.registerProjectHandlers();

    // 系统管理处理器
    this.registerSystemHandlers();

    console.log('✅ Basic IPC handlers registered');
  }

  /**
   * 注册所有 IPC 处理器（需要项目服务的处理器）
   */
  static register(versionCraftService: VersionCraftService): void {
    this.versionCraftService = versionCraftService;

    // 版本管理处理器
    this.registerVersionHandlers();

    // 构建管理处理器
    this.registerBuildHandlers();

    // 回滚管理处理器
    this.registerRollbackHandlers();

    // 热更新管理处理器
    this.registerHotUpdateHandlers();

    // 部署管理处理器
    this.registerDeployHandlers();

    // 配置管理处理器
    this.registerConfigHandlers();

    console.log('✅ All project-specific IPC handlers registered');
  }

  // ==================== 项目管理处理器 ====================

  private static registerProjectHandlers(): void {
    ipcMain.handle(IPC_COMMANDS.PROJECT.SELECT_DIRECTORY, this.handleProjectSelectDirectory.bind(this));
    ipcMain.handle(IPC_COMMANDS.PROJECT.GET_CURRENT, this.handleProjectGetCurrent.bind(this));
    ipcMain.handle(IPC_COMMANDS.PROJECT.GET_INFO, this.handleProjectGetInfo.bind(this));
  }

  private static async handleProjectSelectDirectory(): Promise<APIResponse> {
    try {
      const result = await dialog.showOpenDialog({
        properties: ['openDirectory'],
        title: '选择 Version-Craft 项目目录'
      });

      if (!result.canceled && result.filePaths.length > 0) {
        const projectPath = result.filePaths[0];

        // 验证是否为有效的 Version-Craft 项目
        const configPath = join(projectPath, 'version-craft.config.json');
        if (await fs.pathExists(configPath)) {
          // 保存当前项目路径
          store.set('currentProject', projectPath);

          // 初始化服务
          this.versionCraftService = new VersionCraftService(projectPath);
          this.projectService = new ProjectService(projectPath);

          // 注册项目相关的 IPC 处理器
          this.register(this.versionCraftService);

          return {
            success: true,
            data: { path: projectPath, message: '项目选择成功' }
          };
        } else {
          return {
            success: false,
            error: '所选目录不是有效的 Version-Craft 项目'
          };
        }
      }

      return { success: false, error: '未选择目录' };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '选择项目目录失败'
      };
    }
  }

  private static async handleProjectGetCurrent(): Promise<APIResponse> {
    try {
      const currentProject = store.get('currentProject') as string | null;
      return {
        success: true,
        data: currentProject
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取当前项目失败'
      };
    }
  }

  private static async handleProjectGetInfo(): Promise<APIResponse> {
    try {
      if (!this.projectService) {
        return { success: false, error: '未选择项目' };
      }

      const info = await this.projectService.getProjectInfo();

      return { success: true, data: info };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取项目信息失败'
      };
    }
  }

  // ==================== 版本管理处理器 ====================

  private static registerVersionHandlers(): void {
    ipcMain.handle(IPC_COMMANDS.VERSION.GET_CURRENT, this.handleVersionGetCurrent.bind(this));
    ipcMain.handle(IPC_COMMANDS.VERSION.BUMP, this.handleVersionBump.bind(this));
    ipcMain.handle(IPC_COMMANDS.VERSION.GET_HISTORY, this.handleVersionGetHistory.bind(this));
    ipcMain.handle(IPC_COMMANDS.VERSION.CREATE_TAG, this.handleVersionCreateTag.bind(this));
    ipcMain.handle(IPC_COMMANDS.VERSION.GENERATE_CHANGELOG, this.handleVersionGenerateChangelog.bind(this));
    ipcMain.handle(IPC_COMMANDS.VERSION.RELEASE, this.handleVersionRelease.bind(this));
    ipcMain.handle(IPC_COMMANDS.VERSION.GET_FORMATTED_LIST, this.handleVersionGetFormattedList.bind(this));
  }

  private static async handleVersionGetCurrent(): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.version.getCurrentVersion();
      return { success: true, data: result };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleVersionBump(event: any, options: any): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.version.bumpVersion(options);
      return { success: true, data: result };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleVersionGetHistory(): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.version.getVersionHistory();
      return { success: true, data: result };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleVersionCreateTag(event: any, options: any): Promise<APIResponse> {
    try {
      // 这里需要实现标签创建逻辑
      // 暂时返回成功响应
      return { 
        success: true, 
        data: { 
          message: `标签 ${options.version || 'current'} 创建成功`,
          tag: options.version || 'current'
        } 
      };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleVersionGenerateChangelog(event: any, options?: any): Promise<APIResponse> {
    try {
      // 这里需要实现变更日志生成逻辑
      // 暂时返回成功响应
      return { 
        success: true, 
        data: { 
          message: '变更日志生成成功',
          path: './CHANGELOG.md'
        } 
      };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleVersionRelease(event: any, options?: any): Promise<APIResponse> {
    try {
      // 这里需要实现版本发布逻辑
      // 暂时返回成功响应
      return { 
        success: true, 
        data: { 
          message: '版本发布成功',
          versionInfo: { version: '1.0.0' }
        } 
      };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleVersionGetFormattedList(): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.version.getVersionHistory();
      // 转换为格式化列表格式
      const formattedList = result.map((item: any) => ({
        version: item.version,
        isCurrent: item.isCurrent || false
      }));
      return { success: true, data: formattedList };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  // ==================== 构建管理处理器 ====================

  private static registerBuildHandlers(): void {
    ipcMain.handle(IPC_COMMANDS.BUILD.START, this.handleBuildStart.bind(this));
    ipcMain.handle(IPC_COMMANDS.BUILD.CANCEL, this.handleBuildCancel.bind(this));
    ipcMain.handle(IPC_COMMANDS.BUILD.GET_STATS, this.handleBuildGetStats.bind(this));
    ipcMain.handle(IPC_COMMANDS.BUILD.CLEAN, this.handleBuildClean.bind(this));
    ipcMain.handle(IPC_COMMANDS.BUILD.GET_HISTORY, this.handleBuildGetHistory.bind(this));
    ipcMain.handle(IPC_COMMANDS.BUILD.BUILD_ALL, this.handleBuildAll.bind(this));
    ipcMain.handle(IPC_COMMANDS.BUILD.BUILD_WEB, this.handleBuildWeb.bind(this));
    ipcMain.handle(IPC_COMMANDS.BUILD.BUILD_ANDROID, this.handleBuildAndroid.bind(this));
    ipcMain.handle(IPC_COMMANDS.BUILD.BUILD_IOS, this.handleBuildIOS.bind(this));
    ipcMain.handle(IPC_COMMANDS.BUILD.BUILD_WINDOWS, this.handleBuildWindows.bind(this));
    ipcMain.handle(IPC_COMMANDS.BUILD.BUILD_MAC, this.handleBuildMac.bind(this));
  }

  private static async handleBuildStart(event: any, platform: string, options?: any): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.build.startBuild(platform, options);
      return { success: true, data: result };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleBuildCancel(event: any, buildId: string): Promise<APIResponse> {
    try {
      // 这里需要实现构建取消逻辑
      return { 
        success: true, 
        data: { message: `构建 ${buildId} 已取消` } 
      };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleBuildGetStats(): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.build.getBuildStats();
      return { success: true, data: result };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleBuildClean(): Promise<APIResponse> {
    try {
      // 这里需要实现构建清理逻辑
      return { 
        success: true, 
        data: { message: '构建输出已清理' } 
      };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleBuildGetHistory(): Promise<APIResponse> {
    try {
      // 这里需要实现构建历史获取逻辑
      return { 
        success: true, 
        data: [] // 暂时返回空数组
      };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleBuildAll(event: any, options?: any): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.build.buildAllPlatforms(options);
      return { success: true, data: result };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleBuildWeb(event: any, options?: any): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.build.buildPlatform('web-mobile', options);
      return { success: true, data: result };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleBuildAndroid(event: any, options?: any): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.build.buildPlatform('android', options);
      return { success: true, data: result };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleBuildIOS(event: any, options?: any): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.build.buildPlatform('ios', options);
      return { success: true, data: result };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleBuildWindows(event: any, options?: any): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.build.buildPlatform('win32', options);
      return { success: true, data: result };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleBuildMac(event: any, options?: any): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.build.buildPlatform('mac', options);
      return { success: true, data: result };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  // ==================== 回滚管理处理器 ====================

  private static registerRollbackHandlers(): void {
    ipcMain.handle(IPC_COMMANDS.ROLLBACK.LIST, this.handleRollbackList.bind(this));
    ipcMain.handle(IPC_COMMANDS.ROLLBACK.ROLLBACK_TO, this.handleRollbackTo.bind(this));
    ipcMain.handle(IPC_COMMANDS.ROLLBACK.ROLLBACK_LAST, this.handleRollbackLast.bind(this));
    ipcMain.handle(IPC_COMMANDS.ROLLBACK.GET_STATUS, this.handleRollbackGetStatus.bind(this));
    ipcMain.handle(IPC_COMMANDS.ROLLBACK.VALIDATE, this.handleRollbackValidate.bind(this));
    ipcMain.handle(IPC_COMMANDS.ROLLBACK.CREATE_CHECKPOINT, this.handleRollbackCreateCheckpoint.bind(this));
  }

  private static async handleRollbackList(): Promise<APIResponse> {
    console.log('🔍 [IPC] handleRollbackList called');
    try {
      console.log('🔍 [IPC] Calling versionCraftService.rollback.getRollbackVersions()...');
      const result = await this.versionCraftService!.rollback.getRollbackVersions();
      console.log('🔍 [IPC] getRollbackVersions result:', result);
      return { success: true, data: result };
    } catch (error) {
      console.error('❌ [IPC] handleRollbackList error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleRollbackTo(event: any, version: string, options?: any): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.rollback.rollbackToVersion(version, options);
      return { success: true, data: result };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleRollbackLast(event: any, options?: any): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.rollback.rollbackToLastVersion(options);
      return { success: true, data: result };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleRollbackGetStatus(): Promise<APIResponse> {
    try {
      // 修复：应该调用 getRollbackStatus() 而不是 getRollbackHistory()
      const result = await this.versionCraftService!.rollback.getRollbackStatus();
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleRollbackValidate(event: any, version: string): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.rollback.validateRollback(version);
      return { success: true, data: result };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  private static async handleRollbackCreateCheckpoint(event: any, name?: string): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.rollback.createRollbackCheckpoint(name);
      return { success: true, data: result };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  // ==================== 热更新管理处理器 ====================

  private static registerHotUpdateHandlers(): void {
    ipcMain.handle(IPC_COMMANDS.HOTUPDATE.GENERATE_MANIFEST, this.handleHotUpdateGenerateManifest.bind(this));
    ipcMain.handle(IPC_COMMANDS.HOTUPDATE.CREATE_PATCH, this.handleHotUpdateCreatePatch.bind(this));
    ipcMain.handle(IPC_COMMANDS.HOTUPDATE.VERIFY, this.handleHotUpdateVerify.bind(this));
    ipcMain.handle(IPC_COMMANDS.HOTUPDATE.CLEAN, this.handleHotUpdateClean.bind(this));
    ipcMain.handle(IPC_COMMANDS.HOTUPDATE.COMPARE, this.handleHotUpdateCompare.bind(this));
    ipcMain.handle(IPC_COMMANDS.HOTUPDATE.GET_CONFIG, this.handleHotUpdateGetConfig.bind(this));
    ipcMain.handle(IPC_COMMANDS.HOTUPDATE.CHECK_CHANGES, this.handleHotUpdateCheckChanges.bind(this));
  }

  private static async handleHotUpdateGenerateManifest(event: any, options?: any): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.hotupdate.generateManifest(options);
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleHotUpdateCreatePatch(event: any, options: any): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.hotupdate.createPatch(options.fromVersion, options.toVersion);
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleHotUpdateVerify(event: any, options: any): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.hotupdate.verifyManifest(options.manifestPath);
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleHotUpdateClean(event: any, options?: any): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.hotupdate.cleanOldVersions(options);
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleHotUpdateCompare(event: any, options: any): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.hotupdate.compareVersions(options.version1, options.version2);
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleHotUpdateGetConfig(): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.hotupdate.getHotUpdateConfig();
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleHotUpdateCheckChanges(event: any, fromVersion: string, toVersion: string): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.hotupdate.compareVersions(fromVersion, toVersion);
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  // ==================== 部署管理处理器 ====================

  private static registerDeployHandlers(): void {
    ipcMain.handle(IPC_COMMANDS.DEPLOY.TO_STAGING, this.handleDeployToStaging.bind(this));
    ipcMain.handle(IPC_COMMANDS.DEPLOY.TO_PRODUCTION, this.handleDeployToProduction.bind(this));
    ipcMain.handle(IPC_COMMANDS.DEPLOY.GET_HISTORY, this.handleDeployGetHistory.bind(this));
    ipcMain.handle(IPC_COMMANDS.DEPLOY.GET_STATUS, this.handleDeployGetStatus.bind(this));
    ipcMain.handle(IPC_COMMANDS.DEPLOY.ROLLBACK, this.handleDeployRollback.bind(this));
    ipcMain.handle(IPC_COMMANDS.DEPLOY.CANCEL, this.handleDeployCancel.bind(this));
  }

  private static async handleDeployToStaging(event: any, platform?: string): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.deploy.deployToEnvironment('staging', platform);
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleDeployToProduction(event: any, platform?: string): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.deploy.deployToEnvironment('production', platform);
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleDeployGetHistory(): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.deploy.getDeploymentHistory();
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleDeployGetStatus(): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.deploy.getDeploymentStatus();
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleDeployRollback(event: any, deploymentId: string): Promise<APIResponse> {
    try {
      // 这里需要实现部署回滚逻辑
      return {
        success: true,
        data: { message: `部署 ${deploymentId} 回滚成功` }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleDeployCancel(event: any, deploymentId: string): Promise<APIResponse> {
    try {
      // 这里需要实现部署取消逻辑
      return {
        success: true,
        data: { message: `部署 ${deploymentId} 已取消` }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  // ==================== 配置管理处理器 ====================

  private static registerConfigHandlers(): void {
    ipcMain.handle(IPC_COMMANDS.CONFIG.GET, this.handleConfigGet.bind(this));
    ipcMain.handle(IPC_COMMANDS.CONFIG.SET, this.handleConfigSet.bind(this));
    ipcMain.handle(IPC_COMMANDS.CONFIG.VALIDATE, this.handleConfigValidate.bind(this));
    ipcMain.handle(IPC_COMMANDS.CONFIG.RESET, this.handleConfigReset.bind(this));
    ipcMain.handle(IPC_COMMANDS.CONFIG.EXPORT, this.handleConfigExport.bind(this));
    ipcMain.handle(IPC_COMMANDS.CONFIG.IMPORT, this.handleConfigImport.bind(this));
    ipcMain.handle(IPC_COMMANDS.CONFIG.INIT, this.handleConfigInit.bind(this));
  }

  private static async handleConfigGet(): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.config.getConfig();
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleConfigSet(event: any, key: string, value: any): Promise<APIResponse> {
    try {
      await this.versionCraftService!.config.setConfig(key, value);
      return {
        success: true,
        data: { message: `配置项 ${key} 设置成功` }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleConfigValidate(): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.config.validateConfig();
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleConfigReset(): Promise<APIResponse> {
    try {
      // 这里需要实现配置重置逻辑
      return {
        success: true,
        data: { message: '配置已重置为默认值' }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleConfigExport(event: any, options: any): Promise<APIResponse> {
    try {
      // 这里需要实现配置导出逻辑
      return {
        success: true,
        data: {
          message: '配置导出成功',
          path: options.filePath
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleConfigImport(event: any, options: any): Promise<APIResponse> {
    try {
      // 这里需要实现配置导入逻辑
      return {
        success: true,
        data: { message: '配置导入成功' }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleConfigInit(): Promise<APIResponse> {
    try {
      // 这里需要实现配置初始化逻辑
      return {
        success: true,
        data: { message: '配置初始化成功' }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  // ==================== 系统管理处理器 ====================

  private static registerSystemHandlers(): void {
    ipcMain.handle(IPC_COMMANDS.SYSTEM.HEALTH_CHECK, this.handleSystemHealthCheck.bind(this));
    ipcMain.handle(IPC_COMMANDS.SYSTEM.GET_SERVICE_INFO, this.handleSystemGetServiceInfo.bind(this));
    ipcMain.handle(IPC_COMMANDS.SYSTEM.GET_PROJECT_INFO, this.handleSystemGetProjectInfo.bind(this));
    ipcMain.handle(IPC_COMMANDS.SYSTEM.SET_PROJECT_PATH, this.handleSystemSetProjectPath.bind(this));
    ipcMain.handle(IPC_COMMANDS.SYSTEM.GET_LOGS, this.handleSystemGetLogs.bind(this));
    ipcMain.handle(IPC_COMMANDS.SYSTEM.OPEN_EXTERNAL, this.handleSystemOpenExternal.bind(this));
    ipcMain.handle(IPC_COMMANDS.SYSTEM.SHOW_ITEM_IN_FOLDER, this.handleSystemShowItemInFolder.bind(this));
    ipcMain.handle(IPC_COMMANDS.SYSTEM.QUIT_APP, this.handleSystemQuitApp.bind(this));
  }

  private static async handleSystemHealthCheck(): Promise<APIResponse> {
    try {
      const result = await this.versionCraftService!.healthCheck();
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleSystemGetServiceInfo(): Promise<APIResponse> {
    try {
      const result = this.versionCraftService!.getServiceInfo();
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleSystemGetProjectInfo(): Promise<APIResponse> {
    try {
      // 这里需要实现项目信息获取逻辑
      return {
        success: true,
        data: {
          name: 'Version-Craft Project',
          path: this.versionCraftService!['projectPath'] || process.cwd(),
          version: '1.0.0'
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleSystemSetProjectPath(event: any, path: string): Promise<APIResponse> {
    try {
      // 这里需要实现项目路径设置逻辑
      return {
        success: true,
        data: { message: `项目路径设置为: ${path}` }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleSystemGetLogs(event: any, level?: string, limit?: number): Promise<APIResponse> {
    try {
      // 这里需要实现日志获取逻辑
      return {
        success: true,
        data: [] // 暂时返回空数组
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleSystemOpenExternal(event: any, url: string): Promise<APIResponse> {
    try {
      await shell.openExternal(url);
      return { success: true, data: { message: '外部链接已打开' } };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '打开外部链接失败'
      };
    }
  }

  private static async handleSystemShowItemInFolder(event: any, path: string): Promise<APIResponse> {
    try {
      shell.showItemInFolder(path);
      return { success: true, data: { message: '文件夹已打开' } };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '打开文件夹失败'
      };
    }
  }

  private static async handleSystemQuitApp(): Promise<APIResponse> {
    try {
      app.quit();
      return { success: true, data: { message: '应用即将退出' } };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '退出应用失败'
      };
    }
  }


  // ==================== 日志管理处理器 ====================

  private static registerLoggerHandlers(): void {
    ipcMain.handle(IPC_COMMANDS.LOGGER.WRITE_TO_FILE, this.handleLoggerWriteToFile.bind(this));
    ipcMain.handle(IPC_COMMANDS.LOGGER.WRITE_TO_TERMINAL, this.handleLoggerWriteToTerminal.bind(this));
  }

  // 日志处理器实现
  private static async handleLoggerWriteToFile(event: any, logData: any): Promise<APIResponse> {
    try {
      // 这里可以实现文件写入逻辑
      console.log('[Logger] Write to file:', logData);
      return {
        success: true,
        data: { message: '日志已写入文件' }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private static async handleLoggerWriteToTerminal(event: any, terminalData: any): Promise<APIResponse> {
    try {
      // 输出到主进程终端
      console.log('\n=== [渲染进程日志] ===');
      console.log(terminalData.message);
      if (terminalData.data) {
        console.log('详细信息:');
        console.log(terminalData.data);
      }
      console.log('========================\n');

      return {
        success: true,
        data: { message: '日志已输出到终端' }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }
}
