import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';
import { IPC_COMMANDS } from '../shared/constants/ipc-commands';

// 定义 API 接口
export interface ElectronAPI {
  // 项目管理
  selectProjectDirectory: () => Promise<{ success: boolean; path?: string; error?: string }>;
  getCurrentProject: () => Promise<{ success: boolean; data?: string | null; error?: string }>;
  getProjectInfo: () => Promise<{ success: boolean; data?: any; error?: string }>;

  // 版本管理
  getCurrentVersion: () => Promise<{ success: boolean; data?: any; error?: string }>;
  getVersionHistory: () => Promise<{ success: boolean; data?: any; error?: string }>;
  bumpVersion: (options: any) => Promise<{ success: boolean; data?: any; error?: string }>;
  rollbackVersion: (targetVersion: string, force?: boolean) => Promise<{ success: boolean; data?: any; error?: string }>;

  // 构建管理
  startBuild: (platform: string, options?: any) => Promise<{ success: boolean; data?: any; error?: string }>;
  cancelBuild: (buildId: string) => Promise<{ success: boolean; data?: any; error?: string }>;

  // 系统操作
  openExternal: (url: string) => Promise<void>;
  showItemInFolder: (path: string) => Promise<void>;
  quitApp: () => Promise<void>;

  // IPC 通信
  invoke: (channel: string, ...args: any[]) => Promise<any>;
  on: (channel: string, callback: (data: any) => void) => void;
  off: (channel: string, callback?: (data: any) => void) => void;

  // 事件监听（兼容性保留）
  onBuildProgress: (callback: (data: any) => void) => void;
  onVersionChanged: (callback: (data: any) => void) => void;
  offBuildProgress: () => void;
  offVersionChanged: () => void;
}

// 暴露安全的 API 到渲染进程
const electronAPI: ElectronAPI = {
  // 项目管理
  selectProjectDirectory: () => ipcRenderer.invoke(IPC_COMMANDS.PROJECT.SELECT_DIRECTORY),
  getCurrentProject: () => ipcRenderer.invoke(IPC_COMMANDS.PROJECT.GET_CURRENT),
  getProjectInfo: () => ipcRenderer.invoke(IPC_COMMANDS.PROJECT.GET_INFO),

  // 版本管理 - 已迁移到新的命名空间
  getCurrentVersion: () => ipcRenderer.invoke(IPC_COMMANDS.VERSION.GET_CURRENT),
  getVersionHistory: () => ipcRenderer.invoke(IPC_COMMANDS.VERSION.GET_HISTORY),
  bumpVersion: (options) => ipcRenderer.invoke(IPC_COMMANDS.VERSION.BUMP, options),
  rollbackVersion: (targetVersion, force) => ipcRenderer.invoke(IPC_COMMANDS.ROLLBACK.ROLLBACK_TO, targetVersion, { force }),

  // 构建管理 - 已迁移到新的命名空间
  startBuild: (platform, options) => ipcRenderer.invoke(IPC_COMMANDS.BUILD.START, platform, options),
  cancelBuild: (buildId) => ipcRenderer.invoke(IPC_COMMANDS.BUILD.CANCEL, buildId),

  // 系统操作
  openExternal: (url) => ipcRenderer.invoke(IPC_COMMANDS.SYSTEM.OPEN_EXTERNAL, url),
  showItemInFolder: (path) => ipcRenderer.invoke(IPC_COMMANDS.SYSTEM.SHOW_ITEM_IN_FOLDER, path),
  quitApp: () => ipcRenderer.invoke(IPC_COMMANDS.SYSTEM.QUIT_APP),

  // IPC 通信
  invoke: (channel, ...args) => ipcRenderer.invoke(channel, ...args),
  on: (channel, callback) => {
    ipcRenderer.on(channel, (event, data) => callback(data));
  },
  off: (channel, callback) => {
    if (callback) {
      ipcRenderer.removeListener(channel, callback);
    } else {
      ipcRenderer.removeAllListeners(channel);
    }
  },

  // 事件监听（兼容性保留）
  onBuildProgress: (callback) => {
    ipcRenderer.on(IPC_COMMANDS.EVENTS.BUILD_PROGRESS, (event, data) => callback(data));
  },
  onVersionChanged: (callback) => {
    ipcRenderer.on(IPC_COMMANDS.EVENTS.VERSION_CHANGED, (event, data) => callback(data));
  },
  offBuildProgress: () => {
    ipcRenderer.removeAllListeners(IPC_COMMANDS.EVENTS.BUILD_PROGRESS);
  },
  offVersionChanged: () => {
    ipcRenderer.removeAllListeners(IPC_COMMANDS.EVENTS.VERSION_CHANGED);
  }
};

// 将 API 暴露到全局对象
contextBridge.exposeInMainWorld('electronAPI', electronAPI);

// 类型声明 (用于 TypeScript)
declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}
