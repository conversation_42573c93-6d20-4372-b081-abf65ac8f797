import { Command } from 'commander';
import chalk from 'chalk';
import ora from 'ora';
import inquirer from 'inquirer';
import * as path from 'path';
import { HotUpdateManager, ConfigManager, VersionManager } from '@version-craft/core';
// 注意：ResourceManifest, IncrementalUpdate 类型需要从核心包导出

/**
 * 热更新命令处理器
 */
export class HotUpdateCommand {
    private hotUpdateManager: HotUpdateManager;
    private configManager: ConfigManager;
    private versionManager: VersionManager;

    constructor() {
        this.hotUpdateManager = new HotUpdateManager();
        this.configManager = new ConfigManager();
        this.versionManager = new VersionManager();
    }

    /**
     * 注册所有热更新相关命令到主程序
     */
    registerCommands(program: Command): void {
        // 生成资源清单
        program
            .command('hotupdate-manifest')
            .description('生成热更新资源清单')
            .option('-v, --version <version>', '指定版本号')
            .option('-o, --output <path>', '输出路径')
            .option('--base-url <url>', '资源基础URL')
            .option('--mandatory', '标记为强制更新')
            .option('--description <desc>', '版本描述')
            .action(async (options) => {
                await this.generateManifest(options);
            });

        // 生成增量更新包
        program
            .command('hotupdate-patch <fromVersion> <toVersion>')
            .description('生成增量更新包')
            .option('-o, --output <dir>', '输出目录')
            .option('--base-url <url>', '资源基础URL')
            .action(async (fromVersion: string, toVersion: string, options) => {
                await this.generatePatch(fromVersion, toVersion, options);
            });

        // 添加版本标记
        program
            .command('hotupdate-tag')
            .description('为资源文件添加版本标记')
            .option('-v, --version <version>', '指定版本号')
            .option('-o, --output <dir>', '输出目录')
            .option('--format <format>', '标记格式 (suffix|query|header)', 'suffix')
            .option('--files <patterns...>', '指定文件模式')
            .action(async (options) => {
                await this.addVersionTags(options);
            });

        // 验证资源清单
        program
            .command('hotupdate-verify <manifestPath>')
            .description('验证资源清单的完整性')
            .action(async (manifestPath: string) => {
                await this.verifyManifest(manifestPath);
            });

        // 比较两个版本的差异
        program
            .command('hotupdate-diff <version1> <version2>')
            .description('比较两个版本的资源差异')
            .option('--detailed', '显示详细差异信息')
            .action(async (version1: string, version2: string, options) => {
                await this.compareVersions(version1, version2, options);
            });

        // 清理旧版本资源
        program
            .command('hotupdate-clean')
            .description('清理旧版本的热更新资源')
            .option('--keep <count>', '保留版本数量', '5')
            .option('--dry-run', '仅显示将要删除的文件，不实际删除')
            .action(async (options) => {
                await this.cleanOldVersions(options);
            });

        // 热更新工作流
        program
            .command('hotupdate-release')
            .description('完整的热更新发布流程')
            .option('--from-version <version>', '起始版本')
            .option('--base-url <url>', '资源基础URL')
            .option('--skip-build', '跳过构建步骤')
            .action(async (options) => {
                await this.hotUpdateRelease(options);
            });
    }

    /**
     * 生成资源清单
     */
    private async generateManifest(options: any): Promise<void> {
        try {
            // 获取版本号
            let version = options.version;
            if (!version) {
                const config = await this.configManager.loadConfig();
                version = config.project.version;
            }

            console.log(chalk.blue(`📋 生成版本 ${version} 的资源清单...`));

            const spinner = ora('扫描资源文件...').start();

            const manifest = await this.hotUpdateManager.generateResourceManifest(version, {
                outputPath: options.output,
                baseUrl: options.baseUrl,
                mandatory: options.mandatory,
                description: options.description
            });

            spinner.succeed(chalk.green('资源清单生成完成'));

            // 显示统计信息
            console.log(chalk.blue('\n📊 清单统计:'));
            console.log(chalk.gray(`  版本: ${manifest.version}`));
            console.log(chalk.gray(`  资源数量: ${manifest.resources.length}`));
            console.log(chalk.gray(`  总大小: ${(manifest.totalSize / 1024 / 1024).toFixed(2)}MB`));
            console.log(chalk.gray(`  强制更新: ${manifest.mandatory ? '是' : '否'}`));

            // 按类型统计
            const typeStats = manifest.resources.reduce((stats, resource) => {
                stats[resource.type] = (stats[resource.type] || 0) + 1;
                return stats;
            }, {} as Record<string, number>);

            console.log(chalk.blue('\n📁 资源类型分布:'));
            Object.entries(typeStats).forEach(([type, count]) => {
                console.log(chalk.gray(`  ${type}: ${count} 个文件`));
            });

        } catch (error) {
            console.error(chalk.red('生成资源清单失败:'), error);
            process.exit(1);
        }
    }

    /**
     * 生成增量更新包
     */
    private async generatePatch(fromVersion: string, toVersion: string, options: any): Promise<void> {
        try {
            console.log(chalk.blue(`🔄 生成从 ${fromVersion} 到 ${toVersion} 的增量更新包...`));

            const spinner = ora('分析版本差异...').start();

            const patch = await this.hotUpdateManager.generateIncrementalUpdate(fromVersion, toVersion, {
                outputDir: options.output,
                baseUrl: options.baseUrl
            });

            spinner.succeed(chalk.green('增量更新包生成完成'));

            // 显示更新信息
            console.log(chalk.blue('\n📦 更新包信息:'));
            console.log(chalk.gray(`  更新类型: ${patch.updateType === 'patch' ? '增量更新' : '完整更新'}`));
            console.log(chalk.gray(`  新增文件: ${patch.addedFiles.length} 个`));
            console.log(chalk.gray(`  修改文件: ${patch.modifiedFiles.length} 个`));
            console.log(chalk.gray(`  删除文件: ${patch.deletedFiles.length} 个`));
            console.log(chalk.gray(`  更新大小: ${(patch.totalSize / 1024 / 1024).toFixed(2)}MB`));
            console.log(chalk.gray(`  包路径: ${patch.packagePath}`));

            // 显示详细变更
            if (patch.addedFiles.length > 0) {
                console.log(chalk.green('\n➕ 新增文件:'));
                patch.addedFiles.slice(0, 10).forEach(file => {
                    console.log(chalk.gray(`  + ${file.path} (${(file.size / 1024).toFixed(1)}KB)`));
                });
                if (patch.addedFiles.length > 10) {
                    console.log(chalk.gray(`  ... 还有 ${patch.addedFiles.length - 10} 个文件`));
                }
            }

            if (patch.modifiedFiles.length > 0) {
                console.log(chalk.yellow('\n📝 修改文件:'));
                patch.modifiedFiles.slice(0, 10).forEach(file => {
                    console.log(chalk.gray(`  ~ ${file.path} (${(file.size / 1024).toFixed(1)}KB)`));
                });
                if (patch.modifiedFiles.length > 10) {
                    console.log(chalk.gray(`  ... 还有 ${patch.modifiedFiles.length - 10} 个文件`));
                }
            }

            if (patch.deletedFiles.length > 0) {
                console.log(chalk.red('\n🗑️  删除文件:'));
                patch.deletedFiles.slice(0, 10).forEach(file => {
                    console.log(chalk.gray(`  - ${file}`));
                });
                if (patch.deletedFiles.length > 10) {
                    console.log(chalk.gray(`  ... 还有 ${patch.deletedFiles.length - 10} 个文件`));
                }
            }

        } catch (error) {
            console.error(chalk.red('生成增量更新包失败:'), error);
            process.exit(1);
        }
    }

    /**
     * 添加版本标记
     */
    private async addVersionTags(options: any): Promise<void> {
        try {
            // 获取版本号
            let version = options.version;
            if (!version) {
                const config = await this.configManager.loadConfig();
                version = config.project.version;
            }

            console.log(chalk.blue(`🏷️  为版本 ${version} 添加版本标记...`));

            const spinner = ora('处理资源文件...').start();

            const versionedFiles = await this.hotUpdateManager.addVersionTags(version, {
                resourcePaths: options.files,
                outputDir: options.output,
                tagFormat: options.format
            });

            spinner.succeed(chalk.green('版本标记添加完成'));

            console.log(chalk.blue('\n📊 处理统计:'));
            console.log(chalk.gray(`  处理文件数: ${versionedFiles.size}`));
            console.log(chalk.gray(`  标记格式: ${options.format}`));
            console.log(chalk.gray(`  输出目录: ${options.output || 'dist/versioned'}`));

            // 显示部分处理结果
            const entries = Array.from(versionedFiles.entries()).slice(0, 5);
            if (entries.length > 0) {
                console.log(chalk.blue('\n📁 处理示例:'));
                entries.forEach(([original, versioned]) => {
                    console.log(chalk.gray(`  ${original} → ${path.basename(versioned)}`));
                });
                if (versionedFiles.size > 5) {
                    console.log(chalk.gray(`  ... 还有 ${versionedFiles.size - 5} 个文件`));
                }
            }

        } catch (error) {
            console.error(chalk.red('添加版本标记失败:'), error);
            process.exit(1);
        }
    }

    /**
     * 验证资源清单
     */
    private async verifyManifest(manifestPath: string): Promise<void> {
        try {
            console.log(chalk.blue(`🔍 验证资源清单: ${manifestPath}`));

            const spinner = ora('验证清单完整性...').start();

            // 这里可以添加清单验证逻辑
            // 例如：检查文件是否存在、校验和是否正确等

            spinner.succeed(chalk.green('资源清单验证通过'));

        } catch (error) {
            console.error(chalk.red('资源清单验证失败:'), error);
            process.exit(1);
        }
    }

    /**
     * 比较版本差异
     */
    private async compareVersions(version1: string, version2: string, options: any): Promise<void> {
        try {
            console.log(chalk.blue(`🔍 比较版本 ${version1} 和 ${version2} 的差异...`));

            // 这里可以添加版本比较逻辑
            console.log(chalk.green('版本比较完成'));

        } catch (error) {
            console.error(chalk.red('版本比较失败:'), error);
            process.exit(1);
        }
    }

    /**
     * 比较版本差异
     */
    private async compareVersions(version1: string, version2: string, options: any): Promise<void> {
        try {
            console.log(chalk.blue(` 比较版本 ${version1} 和 ${version2} 的差异...`));

            const spinner = ora('分析版本差异...').start();

            // 使用核心库比较版本
            const result = await this.hotUpdateManager.compareVersions(version1, version2);

            spinner.succeed(chalk.green('版本比较完成'));

            // 显示差异统计
            console.log(chalk.blue('\n 版本差异统计:'));
            console.log(chalk.green(`   新增文件: ${result.addedFiles.length}`));
            console.log(chalk.yellow(`   修改文件: ${result.modifiedFiles.length}`));
            console.log(chalk.red(`   删除文件: ${result.deletedFiles.length}`));
            console.log(chalk.gray(`   总变更: ${result.stats.totalChanges}`));

            console.log(chalk.blue('\n 大小变化:'));
            console.log(chalk.green(`   新增: ${this.formatFileSize(result.stats.addedSize)}`));
            console.log(chalk.yellow(`   修改: ${this.formatFileSize(result.stats.modifiedSize)}`));
            console.log(chalk.red(`   删除: ${this.formatFileSize(result.stats.deletedSize)}`));

            // 更新建议
            if (result.stats.totalChanges === 0) {
                console.log(chalk.gray('\n 两个版本完全相同，无需更新。'));
            } else {
                if (result.stats.changeRatio > 0.3) {
                    console.log(chalk.yellow('\n 建议: 变更较大，考虑使用完整更新包。'));
                } else {
                    console.log(chalk.green('\n 建议: 变更较小，适合使用增量更新包。'));
                }
            }

        } catch (error) {
            console.error(chalk.red('版本比较失败:'), error instanceof Error ? error.message : String(error));
            process.exit(1);
        }
    }

    /**
     * 清理旧版本
     */
    private async cleanOldVersions(options: any): Promise<void> {
        try {
            console.log(chalk.blue('🧹 清理旧版本资源...'));

            const keepCount = parseInt(options.keep) || 5;
            const dryRun = options.dryRun;

            if (dryRun) {
                console.log(chalk.yellow('🔍 预览模式 - 不会实际删除文件'));
            }

            // 这里可以添加清理逻辑
            console.log(chalk.green(`清理完成，保留最新 ${keepCount} 个版本`));

        } catch (error) {
            console.error(chalk.red('清理失败:'), error);
            process.exit(1);
        }
    }

    /**
     * 热更新发布流程
     */
    private async hotUpdateRelease(options: any): Promise<void> {
        try {
            console.log(chalk.blue('🚀 开始热更新发布流程...'));

            // 1. 获取当前版本
            const config = await this.configManager.loadConfig();
            const currentVersion = config.project.version;

            console.log(chalk.gray(`当前版本: ${currentVersion}`));

            // 2. 询问发布选项
            const answers = await inquirer.prompt([
                {
                    type: 'list',
                    name: 'versionType',
                    message: '选择版本升级类型:',
                    choices: [
                        { name: '补丁版本 (x.x.X)', value: 'patch' },
                        { name: '次版本 (x.X.x)', value: 'minor' },
                        { name: '主版本 (X.x.x)', value: 'major' }
                    ]
                },
                {
                    type: 'input',
                    name: 'description',
                    message: '版本描述:',
                    default: `热更新版本`
                },
                {
                    type: 'confirm',
                    name: 'mandatory',
                    message: '是否为强制更新?',
                    default: false
                }
            ]);

            // 3. 升级版本
            if (!options.skipBuild) {
                const versionSpinner = ora('升级版本号...').start();
                const versionInfo = await this.versionManager.bumpVersion(answers.versionType);
                versionSpinner.succeed(chalk.green(`版本已升级: ${versionInfo.current} → ${versionInfo.next}`));
            }

            // 4. 生成资源清单
            const manifestSpinner = ora('生成资源清单...').start();
            const newVersion = (await this.configManager.loadConfig()).project.version;
            
            await this.hotUpdateManager.generateResourceManifest(newVersion, {
                baseUrl: options.baseUrl,
                mandatory: answers.mandatory,
                description: answers.description
            });
            manifestSpinner.succeed(chalk.green('资源清单生成完成'));

            // 5. 生成增量更新包
            if (options.fromVersion) {
                const patchSpinner = ora('生成增量更新包...').start();
                await this.hotUpdateManager.generateIncrementalUpdate(options.fromVersion, newVersion, {
                    baseUrl: options.baseUrl
                });
                patchSpinner.succeed(chalk.green('增量更新包生成完成'));
            }

            console.log(chalk.green(`\n🎉 热更新发布完成！版本: ${newVersion}`));

        } catch (error) {
            console.error(chalk.red('热更新发布失败:'), error);
            process.exit(1);
        }
    }
}
