import { Command } from 'commander';
import chalk from 'chalk';
import inquirer from 'inquirer';
import ora from 'ora';
import { RollbackManager, RollbackInfo, RollbackOptions, RollbackRecord } from '@version-craft/core';

export class RollbackCommand {
  private rollbackManager: RollbackManager;

  constructor() {
    this.rollbackManager = new RollbackManager();
  }

  /**
   * 注册所有回滚相关命令到主程序
   */
  registerCommands(program: Command): void {
    // 列出可回滚版本
    program
      .command('rollback-list')
      .description('列出可回滚的版本')
      .option('-l, --limit <number>', '限制显示数量', '10')
      .action(async (options) => {
        await this.listRollbackVersions(options);
      });

    // 回滚到指定版本
    program
      .command('rollback-to <version>')
      .description('回滚到指定版本')
      .option('-p, --platform <platform>', '指定平台')
      .option('-e, --environment <environment>', '指定环境', 'staging')
      .option('-f, --force', '强制回滚，跳过确认')
      .option('--skip-build', '跳过构建步骤')
      .option('--skip-deploy', '跳过部署步骤')
      .action(async (version: string, options) => {
        await this.rollbackToVersion(version, options);
      });

    // 快速回滚到上一个版本
    program
      .command('rollback-last')
      .description('回滚到上一个版本')
      .option('-p, --platform <platform>', '指定平台')
      .option('-e, --environment <environment>', '指定环境', 'staging')
      .action(async (options) => {
        await this.rollbackToLast(options);
      });

    // 显示回滚状态和历史
    program
      .command('rollback-status')
      .description('显示回滚状态和历史')
      .option('-l, --limit <number>', '限制显示数量', '10')
      .action(async (options) => {
        await this.showRollbackStatus(options);
      });

    // 创建回滚检查点
    program
      .command('rollback-checkpoint [name]')
      .description('创建回滚检查点')
      .action(async (name?: string) => {
        await this.createCheckpoint(name);
      });

    // 验证回滚可行性
    program
      .command('rollback-validate <version>')
      .description('验证回滚到指定版本的可行性')
      .action(async (version: string) => {
        await this.validateRollback(version);
      });
  }

  private async listRollbackVersions(options?: any): Promise<void> {
    try {
      const spinner = ora('获取可回滚版本...').start();
      const rollbackVersions = await this.rollbackManager.getRollbackVersions();
      spinner.stop();

      if (rollbackVersions.length === 0) {
        console.log(chalk.yellow('暂无可回滚版本'));
        return;
      }

      console.log(chalk.blue(' 可回滚版本:'));
      console.log('');

      for (const rollbackInfo of rollbackVersions) {
        const status = rollbackInfo.buildExists ? '✓' : '✗';
        const tagStatus = rollbackInfo.tagExists ? '🏷️' : '  ';

        console.log(chalk.gray(`  ${rollbackInfo.version.padEnd(12)} ${status} ${tagStatus} ${rollbackInfo.date}`));
      }

      console.log(chalk.gray('\n说明: ✓=构建可用 🏷️=Git标签存在'));

    } catch (error) {
      console.error(chalk.red('获取版本列表失败:'), error instanceof Error ? error.message : String(error));
    }
  }

  private async rollbackToVersion(version: string, options?: any): Promise<void> {
    try {
      console.log(chalk.blue(` 回滚到版本: ${version}`));

      const spinner = ora('验证回滚可行性...').start();

      // 使用核心库验证回滚
      const validation = await this.rollbackManager.validateRollback(version);

      if (!validation.valid) {
        spinner.fail(chalk.red('回滚验证失败'));
        console.log(chalk.red('\n❌ 发现问题:'));
        validation.issues.forEach(issue => {
          console.log(chalk.red(`   • ${issue}`));
        });
        return;
      }

      spinner.succeed(chalk.green('回滚验证通过'));

      // 显示回滚信息
      console.log(chalk.gray(`   目标版本: ${version}`));

      if (validation.warnings.length > 0) {
        console.log(chalk.yellow('\n⚠️  警告:'));
        validation.warnings.forEach(warning => {
          console.log(chalk.yellow(`   • ${warning}`));
        });
      }

      // 确认回滚
      if (!options?.force) {
        const { confirm } = await inquirer.prompt([
          {
            type: 'confirm',
            name: 'confirm',
            message: chalk.yellow(`确定要回滚到 ${version} 吗？`),
            default: false
          }
        ]);

        if (!confirm) {
          console.log(chalk.gray('回滚已取消'));
          return;
        }
      }

      // 执行回滚
      const rollbackSpinner = ora('执行回滚...').start();

      const rollbackOptions: RollbackOptions = {
        force: options?.force || false,
        skipBuild: options?.skipBuild || false,
        skipDeploy: options?.skipDeploy || false,
        platform: options?.platform,
        environment: options?.environment || 'staging'
      };

      const result = await this.rollbackManager.rollbackToVersion(version, rollbackOptions);

      rollbackSpinner.succeed(chalk.green(`成功回滚到版本 ${version}`));

      // 显示回滚结果
      console.log(chalk.blue('\n 回滚详情:'));
      console.log(chalk.gray(`   目标版本: ${result.toVersion}`));
      console.log(chalk.gray(`   回滚时间: ${result.timestamp}`));
      console.log(chalk.gray(`   回滚原因: ${result.reason || '手动回滚'}`));

    } catch (error) {
      console.error(chalk.red('回滚失败:'), error instanceof Error ? error.message : String(error));
    }
  }

  private async rollbackToLast(options?: any): Promise<void> {
    try {
      const spinner = ora('查找上一个版本...').start();
      // 直接使用核心库的 rollbackToLastVersion 方法
      const result = await this.rollbackManager.rollbackToLastVersion({
        ...options,
        force: false
      });

      spinner.succeed(chalk.green(`成功回滚到上一个版本: ${result.toVersion}`));

      // 显示回滚结果
      console.log(chalk.blue('\n 回滚详情:'));
      console.log(chalk.gray(`   从版本: ${result.fromVersion}`));
      console.log(chalk.gray(`   到版本: ${result.toVersion}`));
      console.log(chalk.gray(`   回滚时间: ${result.timestamp}`));

    } catch (error) {
      console.error(chalk.red('回滚失败:'), error instanceof Error ? error.message : String(error));
    }
  }

  private async showRollbackStatus(options: any): Promise<void> {
    try {
      const spinner = ora('获取回滚历史...').start();

      const limit = parseInt(options?.limit || '10');
      const history = await this.rollbackManager.getRollbackHistory(limit);

      spinner.succeed(chalk.green('回滚历史获取完成'));

      if (history.length === 0) {
        console.log(chalk.yellow('暂无回滚历史记录'));
        return;
      }

      console.log(chalk.blue('\n 回滚历史:'));
      console.log('');

      history.forEach((record, index) => {
        const timeAgo = this.getTimeAgo(new Date(record.timestamp));
        const status = record.success ? '✅' : '❌';
        console.log(chalk.gray(`${index + 1}. ${status} ${record.fromVersion} → ${record.toVersion} (${timeAgo})`));
        console.log(chalk.gray(`   原因: ${record.reason || '手动回滚'}`));
        if (record.error) {
          console.log(chalk.red(`   错误: ${record.error}`));
        }
        console.log('');
      });

    } catch (error) {
      console.error(chalk.red('获取回滚状态失败:'), error instanceof Error ? error.message : String(error));
    }
  }

  private async createCheckpoint(name?: string): Promise<void> {
    try {
      const spinner = ora('创建回滚检查点...').start();

      const result = await this.rollbackManager.createRollbackCheckpoint(name);

      spinner.succeed(chalk.green('回滚检查点创建成功'));

      console.log(chalk.blue('\n 检查点信息:'));
      console.log(chalk.gray(`   名称: ${result.checkpointName}`));
      console.log(chalk.gray(`   版本: ${result.version}`));
      console.log(chalk.gray(`   标签: ${result.tag}`));

    } catch (error) {
      console.error(chalk.red('创建检查点失败:'), error instanceof Error ? error.message : String(error));
    }
  }

  private async validateRollback(version: string): Promise<void> {
    try {
      const spinner = ora('验证回滚可行性...').start();

      const validation = await this.rollbackManager.validateRollback(version);

      if (validation.valid) {
        spinner.succeed(chalk.green('回滚验证通过'));

        console.log(chalk.blue('\n ✅ 验证结果:'));
        console.log(chalk.green(`   目标版本: ${version}`));
        console.log(chalk.green('   可以安全回滚'));

        if (validation.warnings.length > 0) {
          console.log(chalk.yellow('\n ⚠️  注意事项:'));
          validation.warnings.forEach(warning => {
            console.log(chalk.yellow(`   • ${warning}`));
          });
        }
      } else {
        spinner.fail(chalk.red('回滚验证失败'));

        console.log(chalk.red('\n ❌ 发现问题:'));
        validation.issues.forEach(issue => {
          console.log(chalk.red(`   • ${issue}`));
        });
      }

    } catch (error) {
      console.error(chalk.red('验证回滚失败:'), error instanceof Error ? error.message : String(error));
    }
  }

  /**
   * 计算时间差
   */
  private getTimeAgo(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 60) {
      return `${diffMins} 分钟前`;
    } else if (diffHours < 24) {
      return `${diffHours} 小时前`;
    } else {
      return `${diffDays} 天前`;
    }
  }


}