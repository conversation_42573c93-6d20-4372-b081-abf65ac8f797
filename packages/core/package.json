{"name": "@version-craft/core", "version": "1.0.0", "description": "Version-Craft 核心功能包", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"fs-extra": "^11.1.1", "simple-git": "^3.20.0", "semver": "^7.5.4", "chalk": "^4.1.2", "archiver": "^6.0.1", "execa": "^5.1.1", "dotenv": "^16.3.1", "glob": "^10.3.10"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/semver": "^7.5.4", "@types/archiver": "^6.0.2", "@types/glob": "^8.1.0"}, "files": ["dist/**/*"], "publishConfig": {"access": "public"}}